'use client'

import { useState, useEffect } from 'react'
import { useSession } from 'next-auth/react'
import { useRouter } from 'next/navigation'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import {
  Package,
  Clock,
  CheckCircle,
  Truck,
  CreditCard,
  Eye,
  MapPin,
  ChevronDown,
  ChevronUp,
  Image as ImageIcon,
  ArrowLeft,
  ShoppingBag
} from 'lucide-react'
import { formatCurrency } from '@/lib/utils'
import { getProductImage as getProductImageUtil } from '@/lib/product-images'
import { OrderWithDetails } from '@/types'
import { toast } from 'sonner'
import Image from 'next/image'
import dynamic from 'next/dynamic'

// Dynamic import untuk tracking map
const OrderTrackingMap = dynamic(
  () => import('@/components/orders/order-tracking-map').then(mod => ({ default: mod.OrderTrackingMap })),
  {
    ssr: false,
    loading: () => (
      <div className="h-32 bg-gray-100 rounded-lg animate-pulse flex items-center justify-center">
        <span className="text-gray-500">Loading map...</span>
      </div>
    )
  }
)

// Status mapping - HANYA 3 STATUS UTAMA (sesuai business flow)
const statusConfig = {
  // Status untuk QRIS: Langsung ke PAYMENT_VERIFIED setelah upload bukti
  // Status untuk COD: Langsung ke PAYMENT_VERIFIED (bayar saat terima)
  PAYMENT_VERIFIED: {
    label: 'Menunggu Verifikasi',
    color: 'bg-blue-500',
    textColor: 'text-blue-700',
    bgColor: 'bg-blue-50',
    icon: Clock,
    description: 'Pesanan sedang diverifikasi admin',
    order: 1
  },
  SHIPPED: {
    label: 'Sedang Dikirim',
    color: 'bg-orange-500',
    textColor: 'text-orange-700',
    bgColor: 'bg-orange-50',
    icon: Truck,
    description: 'Pesanan dalam perjalanan ke lokasi Anda',
    order: 2
  },
  DELIVERED: {
    label: 'Selesai',
    color: 'bg-green-500',
    textColor: 'text-green-700',
    bgColor: 'bg-green-50',
    icon: CheckCircle,
    description: 'Pesanan telah sampai di tujuan',
    order: 3
  },
  // Status tambahan untuk edge cases
  CANCELLED: {
    label: 'Dibatalkan',
    color: 'bg-red-500',
    textColor: 'text-red-700',
    bgColor: 'bg-red-50',
    icon: Package,
    description: 'Pesanan dibatalkan',
    order: 0
  }
}

// Status mapping untuk backward compatibility
const statusMapping: { [key: string]: keyof typeof statusConfig } = {
  'PENDING_PAYMENT': 'PAYMENT_VERIFIED', // Map old status to new
  'PAYMENT_VERIFIED': 'PAYMENT_VERIFIED',
  'SHIPPED': 'SHIPPED',
  'DELIVERED': 'DELIVERED',
  'CANCELLED': 'CANCELLED'
}

function OrderCard({ order }: { order: OrderWithDetails }) {
  const [isExpanded, setIsExpanded] = useState(false)
  const [loading, setLoading] = useState(false)
  const [showPaymentProof, setShowPaymentProof] = useState(false)

  // Enhanced status handling with mapping
  const getOrderStatus = () => {
    const rawStatus = order.status as string

    // Map status using the mapping table
    const mappedStatus = statusMapping[rawStatus] || rawStatus as keyof typeof statusConfig

    // Debug logging (only in development)
    if (process.env.NODE_ENV === 'development') {
      console.log('Order status from DB:', rawStatus)
      console.log('Mapped status:', mappedStatus)
      console.log('Available status configs:', Object.keys(statusConfig))
    }

    // Check if mapped status exists in config
    if (statusConfig[mappedStatus]) {
      return statusConfig[mappedStatus]
    }

    // Fallback for unknown status
    console.warn(`Unknown status: ${rawStatus} (mapped: ${mappedStatus}), using fallback`)
    return {
      label: 'Status Tidak Dikenal',
      color: 'bg-gray-500',
      textColor: 'text-gray-700',
      bgColor: 'bg-gray-50',
      icon: Clock,
      description: `Status pesanan: ${rawStatus}`,
      order: 999
    }
  }

  const status = getOrderStatus()
  const StatusIcon = status.icon

  const handleConfirmDelivery = async () => {
    if (!confirm('Apakah Anda yakin pesanan sudah diterima?')) return

    setLoading(true)
    try {
      const response = await fetch(`/api/orders/${order.id}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action: 'confirm_delivery' })
      })

      if (!response.ok) {
        throw new Error('Gagal konfirmasi pengiriman')
      }

      toast.success('Pesanan berhasil dikonfirmasi!')
      window.location.reload()
    } catch (error) {
      toast.error('Gagal konfirmasi pengiriman')
    } finally {
      setLoading(false)
    }
  }

  // Use centralized image utility
  const getProductImage = (productName: string) => {
    return getProductImageUtil(productName)
  }

  return (
    <Card className="mb-4">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className={`w-3 h-3 rounded-full ${status.color}`} />
            <div>
              <CardTitle className="text-lg">{order.orderNumber}</CardTitle>
              <p className="text-sm text-gray-600">
                {new Date(order.createdAt).toLocaleDateString('id-ID', {
                  day: 'numeric',
                  month: 'long',
                  year: 'numeric',
                  hour: '2-digit',
                  minute: '2-digit'
                })}
              </p>
            </div>
          </div>

          <div className="flex items-center space-x-2">
            <Badge className={`${status.color} text-white flex items-center space-x-1`}>
              <StatusIcon className="w-3 h-3" />
              <span>{status.label}</span>
            </Badge>
            <Badge variant="outline" className="text-gray-600">
              {order.paymentMethod === 'QRIS' ? '💳 QRIS' :
               order.paymentMethod === 'MIDTRANS' ? '💎 Midtrans' : '💰 COD'}
            </Badge>
          </div>
        </div>
      </CardHeader>

      <CardContent>
        {/* Order Items Preview */}
        <div className="space-y-3 mb-4">
          {order.orderItems.slice(0, isExpanded ? undefined : 2).map((item) => (
            <div key={item.id} className="flex items-center space-x-3">
              <div className="w-12 h-12 rounded-lg overflow-hidden bg-gray-100">
                <Image
                  src={getProductImage(item.product.name)}
                  alt={item.product.name}
                  width={48}
                  height={48}
                  className="w-full h-full object-cover"
                />
              </div>
              <div className="flex-1">
                <p className="font-medium text-sm">{item.product.name}</p>
                <p className="text-xs text-gray-600">
                  {item.quantity}x {formatCurrency(Number(item.price))}
                </p>
              </div>
              <p className="font-medium text-sm">
                {formatCurrency(Number(item.subtotal))}
              </p>
            </div>
          ))}

          {order.orderItems.length > 2 && !isExpanded && (
            <p className="text-sm text-gray-500 text-center">
              +{order.orderItems.length - 2} produk lainnya
            </p>
          )}
        </div>

        {/* Expand/Collapse Button */}
        <Button
          variant="ghost"
          size="sm"
          onClick={() => setIsExpanded(!isExpanded)}
          className="w-full mb-4"
        >
          {isExpanded ? (
            <>
              <ChevronUp className="w-4 h-4 mr-2" />
              Tutup Detail
            </>
          ) : (
            <>
              <ChevronDown className="w-4 h-4 mr-2" />
              Lihat Detail
            </>
          )}
        </Button>

        {/* Expanded Content */}
        {isExpanded && (
          <div className="space-y-6 border-t pt-6">
            {/* Order Tracking Map */}
            <OrderTrackingMap order={order} />

            {/* Payment Details */}
            <div>
              <h4 className="font-medium text-sm mb-2 flex items-center">
                <CreditCard className="w-4 h-4 mr-2 text-gray-600" />
                Detail Pembayaran
              </h4>
              <div className="bg-gray-50 p-3 rounded-lg space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Subtotal</span>
                  <span>{formatCurrency(Number(order.subtotal))}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>Ongkos Kirim</span>
                  <span>{formatCurrency(Number(order.deliveryFee))}</span>
                </div>
                <Separator />
                <div className="flex justify-between font-medium">
                  <span>Total</span>
                  <span className="text-red-600">{formatCurrency(Number(order.total))}</span>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Action Buttons */}
        <div className="flex flex-wrap gap-2 mt-4">
          {/* QRIS Actions */}
          {order.paymentMethod === 'QRIS' && (
            <>
              {order.payment?.paymentProof && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowPaymentProof(true)}
                  className="flex items-center space-x-1"
                >
                  <ImageIcon className="w-4 h-4" />
                  <span>Lihat Bukti Bayar</span>
                </Button>
              )}
              {order.status === 'SHIPPED' && (
                <Button
                  size="sm"
                  onClick={handleConfirmDelivery}
                  disabled={loading}
                  className="bg-green-600 hover:bg-green-700"
                >
                  <CheckCircle className="w-4 h-4 mr-2" />
                  Konfirmasi Diterima
                </Button>
              )}
            </>
          )}

          {/* COD Actions */}
          {order.paymentMethod === 'COD' && (
            <>
              {order.status === 'SHIPPED' && (
                <Button
                  size="sm"
                  onClick={handleConfirmDelivery}
                  disabled={loading}
                  className="bg-green-600 hover:bg-green-700"
                >
                  <CheckCircle className="w-4 h-4 mr-2" />
                  Konfirmasi Diterima
                </Button>
              )}
            </>
          )}

          {/* Midtrans Actions */}
          {order.paymentMethod === 'MIDTRANS' && (
            <>
              {order.status === 'SHIPPED' && (
                <Button
                  size="sm"
                  onClick={handleConfirmDelivery}
                  disabled={loading}
                  className="bg-green-600 hover:bg-green-700"
                >
                  <CheckCircle className="w-4 h-4 mr-2" />
                  Konfirmasi Diterima
                </Button>
              )}
            </>
          )}
        </div>

        {/* Payment Proof Modal */}
        {showPaymentProof && order.payment?.paymentProof && (
          <>
            <div
              className="fixed inset-0 bg-black/50 z-40"
              onClick={() => setShowPaymentProof(false)}
            />
            <div className="fixed inset-0 flex items-center justify-center z-50 p-4">
              <div className="bg-white rounded-lg p-6 max-w-md w-full">
                <h3 className="text-lg font-semibold mb-4">Bukti Pembayaran</h3>
                <div className="mb-4">
                  <Image
                    src={order.payment.paymentProof}
                    alt="Bukti Pembayaran"
                    width={400}
                    height={300}
                    className="w-full h-auto rounded-lg border"
                  />
                </div>
                <Button
                  onClick={() => setShowPaymentProof(false)}
                  className="w-full"
                >
                  Tutup
                </Button>
              </div>
            </div>
          </>
        )}
      </CardContent>
    </Card>
  )
}

export default function OrdersPage() {
  const { data: session } = useSession()
  const router = useRouter()
  const [orders, setOrders] = useState<OrderWithDetails[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    if (!session) {
      router.push('/auth/signin')
      return
    }

    fetchOrders()
  }, [session, router])

  const fetchOrders = async () => {
    try {
      const response = await fetch('/api/orders')
      const result = await response.json()

      if (result.success) {
        setOrders(result.data)
      } else {
        toast.error('Gagal memuat pesanan')
      }
    } catch (error) {
      toast.error('Gagal memuat pesanan')
    } finally {
      setLoading(false)
    }
  }

  if (!session) {
    return null
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header Section */}
      <div className="bg-white shadow-sm border-b">
        <div className="container mx-auto px-4 max-w-4xl">
          <div className="flex items-center justify-between py-6">
            <div className="flex items-center space-x-4">
              <Button
                variant="ghost"
                onClick={() => router.back()}
                className="p-2"
              >
                <ArrowLeft className="w-5 h-5" />
              </Button>
              <div>
                <h1 className="text-2xl font-bold text-gray-900">Pesanan Saya</h1>
                <p className="text-sm text-gray-600">
                  Pantau status dan riwayat pesanan Anda
                </p>
              </div>
            </div>
            <Button
              onClick={() => router.push('/')}
              className="bg-red-600 hover:bg-red-700"
            >
              <ShoppingBag className="w-4 h-4 mr-2" />
              Lanjutkan Belanja
            </Button>
          </div>
        </div>
      </div>

      {/* Content Section */}
      <div className="container mx-auto px-4 max-w-4xl py-8">

        {loading ? (
          <div className="space-y-4">
            {[1, 2, 3].map((i) => (
              <Card key={i} className="animate-pulse">
                <CardContent className="p-6">
                  <div className="h-4 bg-gray-200 rounded w-1/4 mb-4"></div>
                  <div className="h-3 bg-gray-200 rounded w-1/2 mb-2"></div>
                  <div className="h-3 bg-gray-200 rounded w-1/3"></div>
                </CardContent>
              </Card>
            ))}
          </div>
        ) : orders.length === 0 ? (
          <Card>
            <CardContent className="p-8 text-center">
              <Package className="w-16 h-16 text-gray-300 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                Belum Ada Pesanan
              </h3>
              <p className="text-gray-600 mb-4">
                Anda belum memiliki pesanan. Mulai belanja sekarang!
              </p>
              <Button
                onClick={() => router.push('/')}
                className="bg-red-600 hover:bg-red-700"
              >
                Mulai Belanja
              </Button>
            </CardContent>
          </Card>
        ) : (
          <div>
            {orders.map((order) => (
              <OrderCard key={order.id} order={order} />
            ))}
          </div>
        )}
      </div>
    </div>
  )
}
