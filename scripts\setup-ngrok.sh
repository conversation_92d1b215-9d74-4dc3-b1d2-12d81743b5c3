#!/bin/bash

# Setup script untuk testing Midtrans dengan ngrok
# Jalankan dengan: bash scripts/setup-ngrok.sh

echo "🚀 Setting up Midtrans testing environment with ngrok..."

# Check if ngrok is installed
if ! command -v ngrok &> /dev/null; then
    echo "❌ ngrok is not installed"
    echo "📦 Installing ngrok..."
    npm install -g ngrok
    
    if [ $? -eq 0 ]; then
        echo "✅ ngrok installed successfully"
    else
        echo "❌ Failed to install ngrok"
        exit 1
    fi
else
    echo "✅ ngrok is already installed"
fi

# Check if Next.js dev server is running
if ! curl -s http://localhost:3000 > /dev/null; then
    echo "⚠️  Next.js dev server is not running on port 3000"
    echo "📝 Please start your dev server first:"
    echo "   npm run dev"
    echo ""
    echo "🔄 Starting ngrok anyway (you can start dev server later)..."
else
    echo "✅ Next.js dev server is running on port 3000"
fi

# Start ngrok
echo ""
echo "🌐 Starting ngrok tunnel..."
echo "📝 This will expose your local server to the internet"
echo "📝 Copy the HTTPS URL for Midtrans webhook configuration"
echo ""
echo "🔗 Webhook URLs to configure in Midtrans Dashboard:"
echo "   Notification URL: https://YOUR_NGROK_URL.ngrok.io/api/payments/midtrans/notification"
echo "   Finish URL: https://YOUR_NGROK_URL.ngrok.io/payment/finish"
echo "   Error URL: https://YOUR_NGROK_URL.ngrok.io/payment/error"
echo "   Pending URL: https://YOUR_NGROK_URL.ngrok.io/payment/pending"
echo ""
echo "⚠️  Keep this terminal open while testing"
echo "⚠️  Press Ctrl+C to stop ngrok"
echo ""

# Start ngrok with port 3000
ngrok http 3000
