import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { z } from 'zod'
import { validateDeliveryLocation, getDeliveryZoneInfo } from '@/lib/delivery-pricing'

// Validation schema
const createOrderSchema = z.object({
  items: z.array(z.object({
    productId: z.string(),
    quantity: z.number().min(1),
    price: z.number().min(0)
  })).min(1),
  deliveryAddress: z.string().min(1),
  deliveryLat: z.number().optional(),
  deliveryLng: z.number().optional(),
  notes: z.string().optional(),
  paymentMethod: z.enum(['QRIS', 'COD', 'MIDTRANS']),
  subtotal: z.number().min(0),
  deliveryFee: z.number().min(0),
  total: z.number().min(0)
})

// Generate order number
function generateOrderNumber(): string {
  const timestamp = Date.now().toString()
  const random = Math.random().toString(36).substring(2, 8).toUpperCase()
  return `ACK-${timestamp.slice(-6)}${random}`
}

// GET /api/orders - Get user orders
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const { searchParams } = new URL(request.url)
    const page = parseInt(searchParams.get('page') || '1')
    const limit = parseInt(searchParams.get('limit') || '10')
    const skip = (page - 1) * limit

    const orders = await prisma.order.findMany({
      where: { userId: session.user.id },
      include: {
        orderItems: {
          include: {
            product: true
          }
        },
        payment: true
      },
      orderBy: { createdAt: 'desc' },
      skip,
      take: limit
    })

    const total = await prisma.order.count({
      where: { userId: session.user.id }
    })

    return NextResponse.json({
      success: true,
      data: orders,
      pagination: {
        page,
        limit,
        total,
        totalPages: Math.ceil(total / limit)
      }
    })

  } catch (error) {
    console.error('Get orders error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// POST /api/orders - Create new order
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const validatedData = createOrderSchema.parse(body)

    // Validate delivery location if coordinates provided
    if (validatedData.deliveryLat && validatedData.deliveryLng) {
      const locationValidation = validateDeliveryLocation(
        validatedData.deliveryLat,
        validatedData.deliveryLng
      )

      if (!locationValidation.isValid) {
        return NextResponse.json(
          { error: locationValidation.message },
          { status: 400 }
        )
      }

      // Verify delivery fee matches calculated fee
      const zoneInfo = getDeliveryZoneInfo(validatedData.deliveryLat, validatedData.deliveryLng)
      if (Math.abs(validatedData.deliveryFee - zoneInfo.deliveryFee) > 0.01) {
        return NextResponse.json(
          {
            error: 'Delivery fee mismatch',
            expected: zoneInfo.deliveryFee,
            received: validatedData.deliveryFee
          },
          { status: 400 }
        )
      }
    }

    // Verify products exist and have sufficient stock
    const productIds = validatedData.items.map(item => item.productId)
    const products = await prisma.product.findMany({
      where: { id: { in: productIds } }
    })

    if (products.length !== productIds.length) {
      return NextResponse.json(
        { error: 'Some products not found' },
        { status: 400 }
      )
    }

    // Check stock availability
    for (const item of validatedData.items) {
      const product = products.find(p => p.id === item.productId)
      if (!product || product.stock < item.quantity) {
        return NextResponse.json(
          { error: `Insufficient stock for ${product?.name || 'product'}` },
          { status: 400 }
        )
      }
    }

    // Create order in transaction
    const result = await prisma.$transaction(async (tx) => {
      // Create order
      const order = await tx.order.create({
        data: {
          orderNumber: generateOrderNumber(),
          userId: session.user.id,
          paymentMethod: validatedData.paymentMethod,
          subtotal: validatedData.subtotal,
          deliveryFee: validatedData.deliveryFee,
          total: validatedData.total,
          deliveryAddress: validatedData.deliveryAddress,
          deliveryLat: validatedData.deliveryLat,
          deliveryLng: validatedData.deliveryLng,
          notes: validatedData.notes,
          status: 'PENDING_PAYMENT'
        }
      })

      // Create order items
      const orderItems = await Promise.all(
        validatedData.items.map(item =>
          tx.orderItem.create({
            data: {
              orderId: order.id,
              productId: item.productId,
              quantity: item.quantity,
              price: item.price,
              subtotal: item.price * item.quantity
            }
          })
        )
      )

      // Update product stock
      await Promise.all(
        validatedData.items.map(item =>
          tx.product.update({
            where: { id: item.productId },
            data: {
              stock: {
                decrement: item.quantity
              }
            }
          })
        )
      )

      // Create payment record
      const payment = await tx.payment.create({
        data: {
          orderId: order.id,
          method: validatedData.paymentMethod,
          amount: validatedData.total,
          status: validatedData.paymentMethod === 'COD' ? 'PENDING' : 'PENDING'
        }
      })

      return { order, orderItems, payment }
    })

    return NextResponse.json({
      success: true,
      data: {
        orderId: result.order.id,
        orderNumber: result.order.orderNumber,
        total: result.order.total,
        paymentMethod: result.order.paymentMethod
      }
    })

  } catch (error) {
    console.error('Create order error:', error)

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid data', details: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
