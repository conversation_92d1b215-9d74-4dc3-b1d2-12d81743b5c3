import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { 
  snap, 
  createTransactionParams, 
  generateMidtransOrderId,
  handleMidtransError,
  MidtransTransactionParams 
} from '@/lib/midtrans'
import { z } from 'zod'

const createMidtransPaymentSchema = z.object({
  orderId: z.string(),
})

// POST /api/payments/midtrans/create - Create Midtrans Snap transaction
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const { orderId } = createMidtransPaymentSchema.parse(body)

    // Get order details
    const order = await prisma.order.findFirst({
      where: {
        id: orderId,
        userId: session.user.id,
      },
      include: {
        orderItems: {
          include: {
            product: true,
          },
        },
        payment: true,
        user: true,
      },
    })

    if (!order) {
      return NextResponse.json({ error: 'Order not found' }, { status: 404 })
    }

    // Check if order is eligible for Midtrans payment
    if (order.paymentMethod !== 'MIDTRANS') {
      return NextResponse.json(
        { error: 'Order payment method is not Midtrans' },
        { status: 400 }
      )
    }

    if (order.status !== 'PENDING_PAYMENT') {
      return NextResponse.json(
        { error: 'Order is not pending payment' },
        { status: 400 }
      )
    }

    // Check if Midtrans transaction already exists
    if (order.payment?.midtransSnapToken) {
      return NextResponse.json({
        success: true,
        data: {
          snapToken: order.payment.midtransSnapToken,
          snapUrl: order.payment.midtransSnapUrl,
          orderId: order.payment.midtransOrderId,
        },
      })
    }

    // Generate Midtrans order ID
    const midtransOrderId = generateMidtransOrderId(order.orderNumber)

    // Prepare transaction parameters
    const transactionParams: MidtransTransactionParams = {
      orderId: midtransOrderId,
      grossAmount: Number(order.total),
      customerDetails: {
        first_name: order.user.name || 'Customer',
        email: order.user.email,
        phone: order.user.phone || undefined,
      },
      itemDetails: [
        // Order items
        ...order.orderItems.map((item) => ({
          id: item.product.id,
          price: Number(item.price),
          quantity: item.quantity,
          name: item.product.name,
        })),
        // Delivery fee as separate item
        ...(Number(order.deliveryFee) > 0
          ? [
              {
                id: 'delivery-fee',
                price: Number(order.deliveryFee),
                quantity: 1,
                name: 'Ongkos Kirim',
              },
            ]
          : []),
      ],
    }

    // Create Snap transaction
    const snapTransaction = await snap.createTransaction(
      createTransactionParams(transactionParams)
    )

    // Update payment record with Midtrans data
    const updatedPayment = await prisma.payment.update({
      where: { orderId: order.id },
      data: {
        midtransOrderId,
        midtransSnapToken: snapTransaction.token,
        midtransSnapUrl: snapTransaction.redirect_url,
        updatedAt: new Date(),
      },
    })

    return NextResponse.json({
      success: true,
      data: {
        snapToken: snapTransaction.token,
        snapUrl: snapTransaction.redirect_url,
        orderId: midtransOrderId,
        clientKey: process.env.MIDTRANS_CLIENT_KEY,
      },
    })

  } catch (error) {
    console.error('Create Midtrans payment error:', error)

    // Handle Midtrans specific errors
    try {
      handleMidtransError(error)
    } catch (midtransError) {
      if (midtransError instanceof Error) {
        return NextResponse.json(
          { error: `Midtrans Error: ${midtransError.message}` },
          { status: 400 }
        )
      }
    }

    // Handle validation errors
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid data', details: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
