# Setup script untuk testing Midtrans dengan ngrok (Windows PowerShell)
# Jalankan dengan: powershell -ExecutionPolicy Bypass -File scripts/setup-ngrok.ps1

Write-Host "🚀 Setting up Midtrans testing environment with ngrok..." -ForegroundColor Green

# Check if ngrok is installed
$ngrokInstalled = Get-Command ngrok -ErrorAction SilentlyContinue

if (-not $ngrokInstalled) {
    Write-Host "❌ ngrok is not installed" -ForegroundColor Red
    Write-Host "📦 Installing ngrok..." -ForegroundColor Yellow
    
    try {
        npm install -g ngrok
        Write-Host "✅ ngrok installed successfully" -ForegroundColor Green
    }
    catch {
        Write-Host "❌ Failed to install ngrok" -ForegroundColor Red
        Write-Host "📝 Please install ngrok manually:" -ForegroundColor Yellow
        Write-Host "   1. Download from https://ngrok.com/download" -ForegroundColor Yellow
        Write-Host "   2. Extract to a folder in your PATH" -ForegroundColor Yellow
        Write-Host "   3. Or run: npm install -g ngrok" -ForegroundColor Yellow
        exit 1
    }
}
else {
    Write-Host "✅ ngrok is already installed" -ForegroundColor Green
}

# Check if Next.js dev server is running
try {
    $response = Invoke-WebRequest -Uri "http://localhost:3000" -TimeoutSec 5 -ErrorAction Stop
    Write-Host "✅ Next.js dev server is running on port 3000" -ForegroundColor Green
}
catch {
    Write-Host "⚠️  Next.js dev server is not running on port 3000" -ForegroundColor Yellow
    Write-Host "📝 Please start your dev server first:" -ForegroundColor Yellow
    Write-Host "   npm run dev" -ForegroundColor Yellow
    Write-Host ""
    Write-Host "🔄 Starting ngrok anyway (you can start dev server later)..." -ForegroundColor Yellow
}

# Display configuration instructions
Write-Host ""
Write-Host "🌐 Starting ngrok tunnel..." -ForegroundColor Green
Write-Host "📝 This will expose your local server to the internet" -ForegroundColor Cyan
Write-Host "📝 Copy the HTTPS URL for Midtrans webhook configuration" -ForegroundColor Cyan
Write-Host ""
Write-Host "🔗 Webhook URLs to configure in Midtrans Dashboard:" -ForegroundColor Yellow
Write-Host "   Notification URL: https://YOUR_NGROK_URL.ngrok.io/api/payments/midtrans/notification" -ForegroundColor White
Write-Host "   Finish URL: https://YOUR_NGROK_URL.ngrok.io/payment/finish" -ForegroundColor White
Write-Host "   Error URL: https://YOUR_NGROK_URL.ngrok.io/payment/error" -ForegroundColor White
Write-Host "   Pending URL: https://YOUR_NGROK_URL.ngrok.io/payment/pending" -ForegroundColor White
Write-Host ""
Write-Host "⚠️  Keep this terminal open while testing" -ForegroundColor Red
Write-Host "⚠️  Press Ctrl+C to stop ngrok" -ForegroundColor Red
Write-Host ""

# Start ngrok with port 3000
& ngrok http 3000
