import { NextRequest, NextResponse } from 'next/server'
import { prisma } from '@/lib/prisma'
import { 
  validateSignature, 
  mapMidtransStatus, 
  MidtransNotification 
} from '@/lib/midtrans'

// POST /api/payments/midtrans/notification - Handle Midtrans notification
export async function POST(request: NextRequest) {
  try {
    const notification: MidtransNotification = await request.json()

    console.log('Midtrans notification received:', notification)

    // Validate required fields
    if (!notification.order_id || !notification.status_code || !notification.gross_amount || !notification.signature_key) {
      console.error('Missing required notification fields')
      return NextResponse.json({ error: 'Invalid notification data' }, { status: 400 })
    }

    // Validate signature
    const isValidSignature = validateSignature(
      notification.order_id,
      notification.status_code,
      notification.gross_amount,
      notification.signature_key
    )

    if (!isValidSignature) {
      console.error('Invalid signature for order:', notification.order_id)
      return NextResponse.json({ error: 'Invalid signature' }, { status: 400 })
    }

    // Find payment record by Midtrans order ID
    const payment = await prisma.payment.findFirst({
      where: {
        midtransOrderId: notification.order_id,
      },
      include: {
        order: true,
      },
    })

    if (!payment) {
      console.error('Payment not found for Midtrans order ID:', notification.order_id)
      return NextResponse.json({ error: 'Payment not found' }, { status: 404 })
    }

    // Map Midtrans status to our payment status
    const newPaymentStatus = mapMidtransStatus(
      notification.transaction_status,
      notification.fraud_status
    )

    // Determine order status based on payment status
    let newOrderStatus = payment.order.status
    if (newPaymentStatus === 'SETTLEMENT') {
      newOrderStatus = 'PAYMENT_VERIFIED'
    } else if (['DENY', 'CANCEL', 'EXPIRE', 'FAILED'].includes(newPaymentStatus)) {
      newOrderStatus = 'CANCELLED'
    }

    // Update payment and order in transaction
    await prisma.$transaction(async (tx) => {
      // Update payment record
      await tx.payment.update({
        where: { id: payment.id },
        data: {
          status: newPaymentStatus as any,
          midtransTransactionId: notification.transaction_id,
          midtransPaymentType: notification.payment_type,
          midtransSignatureKey: notification.signature_key,
          midtransRawNotification: JSON.stringify(notification),
          verifiedAt: newPaymentStatus === 'SETTLEMENT' ? new Date() : null,
          updatedAt: new Date(),
        },
      })

      // Update order status if needed
      if (newOrderStatus !== payment.order.status) {
        await tx.order.update({
          where: { id: payment.order.id },
          data: {
            status: newOrderStatus as any,
            updatedAt: new Date(),
          },
        })
      }
    })

    console.log(`Payment updated: ${payment.id}, Status: ${newPaymentStatus}, Order Status: ${newOrderStatus}`)

    // Return success response to Midtrans
    return NextResponse.json({ 
      success: true,
      message: 'Notification processed successfully' 
    })

  } catch (error) {
    console.error('Midtrans notification error:', error)
    
    // Return error response to Midtrans
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}

// GET /api/payments/midtrans/notification - Health check for webhook
export async function GET() {
  return NextResponse.json({ 
    success: true, 
    message: 'Midtrans notification endpoint is active',
    timestamp: new Date().toISOString()
  })
}
