# Dokumentasi Alur Transaksi - Acikoo E-Commerce

## Executive Summary
Dokumen ini menjelaskan secara komprehensif alur transaksi lengkap sistem e-commerce Acikoo, mulai dari proses checkout hingga konfirmasi penerimaan barang oleh customer. Sistem mendukung dua metode pembayaran utama (QRIS dan COD) dengan 6 zona pengiriman yang berbeda, serta dilengkapi dengan dashboard admin untuk manajemen order.

### Key Features
- **Dual Payment System**: QRIS (instant) dan COD (cash on delivery)
- **Zone-based Delivery**: 6 zona pengiriman dengan tarif berbeda (Rp 10.000 - Rp 25.000)
- **Interactive Maps**: Leaflet-based delivery zone selection
- **Real-time Tracking**: Order status tracking untuk customer dan admin
- **Automated Stock Management**: Stock otomatis berkurang saat order dibuat
- **Role-based Access**: Pemisahan akses customer dan admin

### Transaction Flow Overview
```
Customer: Cart → Checkout → Payment → Order Created
Admin: Verify Payment → Ship Order
Customer: Confirm Delivery → Order Complete
```

## Overview Sistem
Acikoo adalah aplikasi e-commerce berbasis Next.js 15 dengan teknologi:
- **Frontend**: Next.js 15, React 18, TypeScript, Tailwind CSS
- **Backend**: Next.js API Routes, NextAuth.js
- **Database**: MySQL dengan Prisma ORM
- **State Management**: Zustand untuk cart management
- **Payment**: QRIS dan Cash on Delivery (COD)
- **Maps**: Leaflet untuk delivery zone mapping
- **Authentication**: NextAuth.js dengan role-based access

## Struktur Database Utama

### Tabel Orders
```sql
- id: String (Primary Key)
- orderNumber: String (Unique, format: ACK-XXXXXX)
- userId: String (Foreign Key ke User)
- status: OrderStatus (PENDING_PAYMENT, PAYMENT_VERIFIED, SHIPPED, DELIVERED, CANCELLED)
- paymentMethod: PaymentMethod (QRIS, COD)
- subtotal: Decimal
- deliveryFee: Decimal
- total: Decimal
- deliveryAddress: String
- deliveryLat: Float
- deliveryLng: Float
- notes: String (Optional)
- createdAt: DateTime
- updatedAt: DateTime
```

### Tabel Payments
```sql
- id: String (Primary Key)
- orderId: String (Foreign Key ke Order, Unique)
- method: PaymentMethod (QRIS, COD)
- status: PaymentStatus (PENDING, VERIFIED, FAILED)
- amount: Decimal
- paymentProof: String (URL bukti pembayaran untuk QRIS)
- verifiedAt: DateTime
- verifiedBy: String (Admin ID yang verifikasi)
- createdAt: DateTime
- updatedAt: DateTime
```

### Tabel OrderItems
```sql
- id: String (Primary Key)
- orderId: String (Foreign Key ke Order)
- productId: String (Foreign Key ke Product)
- quantity: Int
- price: Decimal (Harga saat order dibuat)
- subtotal: Decimal (price * quantity)
```

## Alur Transaksi Lengkap

### 1. FASE CHECKOUT (Customer)

#### 1.1 Persiapan Checkout
**Halaman**: `/checkout`
**Komponen**: `src/app/checkout/page.tsx`

**Proses**:
1. Customer mengakses halaman checkout dari cart
2. Validasi session dan cart items
3. Tampilkan progress indicator (4 tahap: Keranjang → Alamat → Pembayaran → Konfirmasi)

#### 1.2 Pemilihan Alamat Pengiriman
**Komponen**: `DeliveryZoneMap` (dynamic import)
**Library**: `src/lib/delivery-pricing.ts`, `src/lib/store-config.ts`

**Proses**:
1. Customer klik "Buka Map" untuk memilih lokasi
2. Map menampilkan 6 zona pengiriman dengan radius berbeda:
   - Zone 1 (0-4km): Rp 10.000
   - Zone 2 (5km): Rp 13.000
   - Zone 3 (6km): Rp 16.000
   - Zone 4 (7km): Rp 19.000
   - Zone 5 (8km): Rp 22.000
   - Zone 6 (9km): Rp 25.000
3. Validasi lokasi dengan `validateDeliveryLocation()`
4. Hitung ongkir dengan `getDeliveryZoneInfo()`
5. Update state dengan lokasi terpilih

#### 1.3 Pemilihan Metode Pembayaran
**Metode Tersedia**:
- **QRIS**: Pembayaran instant dengan QR Code
- **COD**: Cash on Delivery (bayar saat terima)

**Proses QRIS**:
1. Customer pilih QRIS
2. Tampilkan modal QR Code (`/img/QRIS.png`)
3. Customer scan dan bayar
4. Upload bukti pembayaran (max 5MB, format image)

**Proses COD**:
1. Customer pilih COD
2. Tampilkan syarat & ketentuan COD
3. Customer centang persetujuan

### 2. FASE PEMBUATAN ORDER (Customer)

#### 2.1 API Create Order
**Endpoint**: `POST /api/orders`
**File**: `src/app/api/orders/route.ts`

**Validasi**:
- Session authentication
- Validasi cart items dan stock
- Validasi lokasi pengiriman
- Validasi delivery fee

**Proses Database Transaction**:
```typescript
1. Buat record Order (status: PENDING_PAYMENT)
2. Buat record OrderItems untuk setiap produk
3. Update stock produk (decrement)
4. Buat record Payment (status: PENDING)
```

#### 2.2 Upload Bukti Pembayaran (QRIS Only)
**Endpoint**: `POST /api/payments/upload-proof`
**File**: `src/app/api/payments/upload-proof/route.ts`

**Proses**:
1. Validasi file (image, max 5MB)
2. Generate unique filename: `{orderId}-{timestamp}.{ext}`
3. Simpan ke `/public/uploads/payment-proofs/`
4. Update Payment record dengan paymentProof URL

### 3. FASE VERIFIKASI ADMIN

#### 3.1 Dashboard Admin Orders
**Halaman**: `/admin/orders`
**File**: `src/app/admin/orders/page.tsx`

**Fitur**:
- Tab "Ongoing" dan "Completed"
- Filter berdasarkan customer, status, payment method, tanggal
- Search order number, customer name, produk

#### 3.2 Verifikasi Pembayaran
**Endpoint**: `PATCH /api/admin/orders/[id]`
**File**: `src/app/api/admin/orders/[id]/route.ts`

**Action**: `verify_payment`
**Proses**:
1. Validasi admin session
2. Cek order status = PENDING_PAYMENT
3. Update Order status → PAYMENT_VERIFIED
4. Update Payment status → VERIFIED, verifiedAt, verifiedBy

**Untuk QRIS**: Admin lihat bukti pembayaran sebelum verifikasi
**Untuk COD**: Admin langsung konfirmasi (pembayaran saat delivery)

#### 3.3 Pengiriman Order
**Action**: `ship_order`
**Proses**:
1. Validasi order status = PAYMENT_VERIFIED
2. Update Order status → SHIPPED

#### 3.4 Pembatalan Order
**Action**: `cancel_order`
**Proses**:
1. Validasi order belum DELIVERED/CANCELLED
2. Update Order status → CANCELLED

### 4. FASE TRACKING CUSTOMER

#### 4.1 Halaman Orders Customer
**Halaman**: `/orders`
**File**: `src/app/orders/page.tsx`

**Status Mapping untuk Customer**:
- PENDING_PAYMENT → "Menunggu Verifikasi"
- PAYMENT_VERIFIED → "Menunggu Verifikasi" 
- SHIPPED → "Sedang Dikirim"
- DELIVERED → "Selesai"
- CANCELLED → "Dibatalkan"

#### 4.2 Konfirmasi Penerimaan
**Endpoint**: `PATCH /api/orders/[id]`
**File**: `src/app/api/orders/[id]/route.ts`

**Action**: `confirm_delivery`
**Proses**:
1. Validasi order status = SHIPPED
2. Update Order status → DELIVERED

## Metode Pembayaran Detail

### QRIS (QR Code Indonesian Standard)
**Alur Customer**:
1. Pilih QRIS di checkout
2. Lihat QR Code modal
3. Scan dengan e-wallet (GoPay, OVO, DANA, dll)
4. Bayar sesuai total
5. Screenshot/foto bukti pembayaran
6. Upload bukti di form checkout
7. Submit order

**Alur Admin**:
1. Terima notifikasi order baru
2. Lihat bukti pembayaran yang diupload
3. Verifikasi kesesuaian nominal dan detail
4. Klik "Verifikasi Pembayaran"
5. Order status berubah ke PAYMENT_VERIFIED
6. Siap untuk dikirim

**File Terlibat**:
- Frontend: `src/app/checkout/page.tsx` (upload form)
- API: `src/app/api/payments/upload-proof/route.ts`
- Admin: `src/app/admin/orders/page.tsx` (verifikasi)
- Storage: `/public/uploads/payment-proofs/`

### COD (Cash on Delivery)
**Alur Customer**:
1. Pilih COD di checkout
2. Baca dan setujui syarat & ketentuan:
   - Pembayaran saat barang diterima
   - Siapkan uang pas
   - Pastikan ada yang menerima
   - Barang bisa diperiksa dulu
3. Submit order

**Alur Admin**:
1. Terima order COD
2. Langsung konfirmasi (tidak perlu verifikasi pembayaran)
3. Siapkan barang untuk pengiriman
4. Koordinasi dengan kurir untuk COD

**File Terlibat**:
- Frontend: `src/app/checkout/page.tsx` (terms checkbox)
- Admin: `src/app/admin/orders/page.tsx` (konfirmasi COD)

## Status Order Lifecycle

### Flow Diagram Lengkap
```
┌─────────────────┐
│   CART ITEMS    │
└─────────┬───────┘
          │
          ▼
┌─────────────────┐
│    CHECKOUT     │ ◄── Customer pilih alamat & payment
└─────────┬───────┘
          │
          ▼
┌─────────────────┐
│ PENDING_PAYMENT │ ◄── Order dibuat, menunggu verifikasi
└─────────┬───────┘
          │
          ▼
┌─────────────────┐
│PAYMENT_VERIFIED │ ◄── Admin verifikasi pembayaran
└─────────┬───────┘
          │
          ▼
┌─────────────────┐
│    SHIPPED      │ ◄── Admin kirim barang
└─────────┬───────┘
          │
          ▼
┌─────────────────┐
│   DELIVERED     │ ◄── Customer konfirmasi terima
└─────────────────┘

┌─────────────────┐
│   CANCELLED     │ ◄── Bisa dari PENDING_PAYMENT atau PAYMENT_VERIFIED
└─────────────────┘
```

### State Transition Matrix
| From Status | To Status | Trigger | Actor | API Endpoint |
|-------------|-----------|---------|-------|--------------|
| - | PENDING_PAYMENT | Create Order | Customer | POST /api/orders |
| PENDING_PAYMENT | PAYMENT_VERIFIED | Verify Payment | Admin | PATCH /api/admin/orders/[id] |
| PAYMENT_VERIFIED | SHIPPED | Ship Order | Admin | PATCH /api/admin/orders/[id] |
| SHIPPED | DELIVERED | Confirm Delivery | Customer | PATCH /api/orders/[id] |
| PENDING_PAYMENT | CANCELLED | Cancel Order | Admin | PATCH /api/admin/orders/[id] |
| PAYMENT_VERIFIED | CANCELLED | Cancel Order | Admin | PATCH /api/admin/orders/[id] |

### Payment Status Lifecycle
```
PENDING (Payment record dibuat)
   ↓
VERIFIED (Admin verifikasi - QRIS only)
   ↓
COMPLETED (Order selesai)

FAILED (Jika ada masalah pembayaran)
```

## File dan Komponen Utama

### Mapping Lengkap File per Fase Transaksi

#### FASE 1: CHECKOUT (Customer)
| Komponen | File Path | Fungsi |
|----------|-----------|--------|
| Checkout Page | `src/app/checkout/page.tsx` | Main checkout interface |
| Delivery Map | `src/components/maps/delivery-zone-map.tsx` | Interactive delivery zone selection |
| Cart Store | `src/store/cart.ts` | Cart state management |
| Delivery Pricing | `src/lib/delivery-pricing.ts` | Ongkir calculation |
| Store Config | `src/lib/store-config.ts` | Store settings & zones |

#### FASE 2: ORDER CREATION (API)
| Endpoint | File Path | Fungsi |
|----------|-----------|--------|
| POST /api/orders | `src/app/api/orders/route.ts` | Create new order |
| POST /api/payments/upload-proof | `src/app/api/payments/upload-proof/route.ts` | Upload payment proof |
| GET /api/delivery/calculate | `src/app/api/delivery/calculate/route.ts` | Calculate delivery fee |

#### FASE 3: ADMIN MANAGEMENT
| Komponen | File Path | Fungsi |
|----------|-----------|--------|
| Admin Orders Page | `src/app/admin/orders/page.tsx` | Order management dashboard |
| Admin Layout | `src/app/admin/layout.tsx` | Admin layout wrapper |
| Order Detail API | `src/app/api/admin/orders/[id]/route.ts` | Update order status |
| Orders List API | `src/app/api/admin/orders/route.ts` | Get orders list |

#### FASE 4: CUSTOMER TRACKING
| Komponen | File Path | Fungsi |
|----------|-----------|--------|
| Orders Page | `src/app/orders/page.tsx` | Customer order history |
| Order Detail | `src/app/orders/[id]/page.tsx` | Individual order details |
| Order API | `src/app/api/orders/[id]/route.ts` | Get/update specific order |
| Tracking Map | `src/components/orders/order-tracking-map.tsx` | Order tracking visualization |

### Database Tables dan Relasi
| Tabel | File | Primary Key | Foreign Keys |
|-------|------|-------------|--------------|
| orders | `prisma/schema.prisma` | id | userId |
| order_items | `prisma/schema.prisma` | id | orderId, productId |
| payments | `prisma/schema.prisma` | id | orderId |
| users | `prisma/schema.prisma` | id | - |
| products | `prisma/schema.prisma` | id | categoryId |

### Utility Libraries
| Library | File Path | Fungsi |
|---------|-----------|--------|
| Authentication | `src/lib/auth.ts` | NextAuth configuration |
| Prisma Client | `src/lib/prisma.ts` | Database connection |
| Product Images | `src/lib/product-images.ts` | Image URL generation |
| Utils | `src/lib/utils.ts` | Common utilities |
| Types | `src/types/index.ts` | TypeScript definitions |

## Business Rules dan Validasi

### Order Creation Rules
1. **Minimum Order**: Rp 15.000 (dari `STORE_CONFIG.business.minimumOrder`)
2. **Maximum Quantity**: 50 per item (dari `STORE_CONFIG.business.maxOrderQuantity`)
3. **Stock Validation**: Quantity tidak boleh melebihi stock tersedia
4. **Delivery Zone**: Maksimal 9km dari toko (dari `STORE_CONFIG.delivery.maxDeliveryDistance`)
5. **Order Number**: Format ACK-{timestamp}{random} (unique)

### Payment Rules
#### QRIS
- Bukti pembayaran wajib diupload
- Format file: image only (jpg, png, gif, webp)
- Ukuran maksimal: 5MB
- Admin harus verifikasi manual sebelum order diproses

#### COD (Cash on Delivery)
- Tidak perlu bukti pembayaran
- Customer harus setuju terms & conditions
- Pembayaran dilakukan saat barang diterima
- Admin bisa langsung approve tanpa verifikasi pembayaran

### Delivery Zone Rules
```javascript
// Dari src/lib/store-config.ts
const DELIVERY_ZONES = [
  { name: 'Zone 1 (0-4km)', maxDistance: 4, fee: 10000 },
  { name: 'Zone 2 (5km)', maxDistance: 5, fee: 13000 },
  { name: 'Zone 3 (6km)', maxDistance: 6, fee: 16000 },
  { name: 'Zone 4 (7km)', maxDistance: 7, fee: 19000 },
  { name: 'Zone 5 (8km)', maxDistance: 8, fee: 22000 },
  { name: 'Zone 6 (9km)', maxDistance: 9, fee: 25000 }
]
```

### Status Transition Rules
| Current Status | Allowed Next Status | Required Condition |
|----------------|--------------------|--------------------|
| PENDING_PAYMENT | PAYMENT_VERIFIED | Admin verification |
| PENDING_PAYMENT | CANCELLED | Admin action |
| PAYMENT_VERIFIED | SHIPPED | Admin action |
| PAYMENT_VERIFIED | CANCELLED | Admin action |
| SHIPPED | DELIVERED | Customer confirmation |
| DELIVERED | - | Final status |
| CANCELLED | - | Final status |

### Keamanan dan Validasi

#### Authentication
- NextAuth.js untuk session management
- Role-based access (CUSTOMER, ADMIN)
- API route protection dengan session check
- JWT tokens untuk stateless authentication

#### Input Validation
```typescript
// Zod schemas untuk validasi
const createOrderSchema = z.object({
  items: z.array(z.object({
    productId: z.string(),
    quantity: z.number().min(1),
    price: z.number().min(0)
  })).min(1),
  deliveryAddress: z.string().min(1),
  paymentMethod: z.enum(['QRIS', 'COD']),
  subtotal: z.number().min(0),
  deliveryFee: z.number().min(0),
  total: z.number().min(0)
})
```

#### Database Integrity
- Foreign key constraints untuk relasi
- Database transactions untuk operasi multi-table
- Unique constraints untuk orderNumber
- Cascade delete untuk order items
- Optimistic locking untuk stock updates

## Error Handling

### Customer Side
- Stock tidak cukup → Redirect ke cart
- Lokasi di luar area → Error message
- Upload gagal → Retry mechanism
- Session expired → Redirect login

### Admin Side
- Unauthorized access → 401 error
- Invalid order state → 400 error
- Database error → 500 error dengan logging

## Integrasi dengan Sistem Lain

### Cart Management (Zustand)
**File**: `src/store/cart.ts`
**Fungsi**:
- `addItem()` - Tambah produk ke cart
- `removeItem()` - Hapus produk dari cart
- `updateQuantity()` - Update jumlah produk
- `clearCart()` - Kosongkan cart (setelah checkout berhasil)
- Persistent storage dengan localStorage

### Delivery Zone Mapping
**File**: `src/components/maps/delivery-zone-map.tsx`
**Teknologi**: React Leaflet
**Fitur**:
- Interactive map dengan 6 zona pengiriman
- Real-time distance calculation
- Address geocoding
- Zone color coding
- Click to select location

### Product Stock Management
**Proses**:
1. Saat order dibuat → Stock dikurangi otomatis
2. Jika order dibatalkan → Stock dikembalikan
3. Real-time stock check saat checkout
4. Prevent overselling dengan database constraints

## Monitoring dan Analytics

### Order Tracking
**Customer View**:
- Order status dengan progress indicator
- Estimated delivery time
- Delivery tracking map
- Payment proof display

**Admin View**:
- Real-time order dashboard
- Filter dan search capabilities
- Bulk actions untuk multiple orders
- Customer order history

### Performance Metrics
- Order completion rate
- Payment verification time
- Delivery success rate
- Customer satisfaction tracking

## Troubleshooting Common Issues

### Customer Issues
1. **Upload Bukti Pembayaran Gagal**
   - Check file size (max 5MB)
   - Check file format (image only)
   - Check internet connection
   - Retry upload

2. **Lokasi Tidak Bisa Dipilih**
   - Check GPS permission
   - Ensure location within delivery zones
   - Try manual address input
   - Contact customer service

3. **Order Tidak Muncul**
   - Check login status
   - Refresh page
   - Check order confirmation email
   - Contact support

### Admin Issues
1. **Bukti Pembayaran Tidak Muncul**
   - Check file path permissions
   - Verify upload directory exists
   - Check database payment record
   - Regenerate image URL

2. **Order Status Tidak Update**
   - Check database connection
   - Verify admin permissions
   - Check transaction rollback
   - Review error logs

## API Response Examples

### Create Order Success
```json
{
  "success": true,
  "data": {
    "orderId": "clx1234567890",
    "orderNumber": "ACK-123456",
    "total": 45000,
    "paymentMethod": "QRIS"
  }
}
```

### Order List Response
```json
{
  "success": true,
  "data": [
    {
      "id": "clx1234567890",
      "orderNumber": "ACK-123456",
      "status": "SHIPPED",
      "total": "45000.00",
      "paymentMethod": "QRIS",
      "createdAt": "2024-01-15T10:30:00Z",
      "orderItems": [...],
      "payment": {...}
    }
  ],
  "pagination": {
    "page": 1,
    "limit": 10,
    "total": 25,
    "totalPages": 3
  }
}
```

## Future Enhancements

### Planned Features
1. **Real-time Notifications**
   - WebSocket integration
   - Push notifications
   - Email notifications
   - SMS alerts

2. **Advanced Payment Methods**
   - Bank transfer
   - Credit card integration
   - Installment payments
   - Wallet integration

3. **Delivery Optimization**
   - Route optimization
   - Multiple delivery slots
   - Express delivery options
   - Delivery partner integration

4. **Analytics Dashboard**
   - Sales analytics
   - Customer behavior tracking
   - Inventory forecasting
   - Performance metrics

### Technical Improvements
1. **Performance**
   - Image optimization
   - Caching strategies
   - Database indexing
   - CDN integration

2. **Security**
   - Payment encryption
   - Rate limiting
   - Input sanitization
   - Audit logging

3. **Scalability**
   - Microservices architecture
   - Load balancing
   - Database sharding
   - Queue management

## Configuration dan Environment

### Environment Variables
```bash
# Database
DATABASE_URL="mysql://user:password@localhost:3306/acikoo"

# NextAuth
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="your-secret-key"

# Google OAuth (Optional)
GOOGLE_CLIENT_ID="your-google-client-id"
GOOGLE_CLIENT_SECRET="your-google-client-secret"
```

### Store Configuration
File: `src/lib/store-config.ts`
```typescript
export const STORE_CONFIG = {
  name: 'Acikoo',
  location: {
    address: 'Jl. Tanah Merah No.15, Pluit, Jakarta Utara',
    latitude: -6.1275,
    longitude: 106.7906
  },
  delivery: {
    maxDeliveryDistance: 9, // km
    freeDeliveryThreshold: 50000 // IDR
  },
  business: {
    minimumOrder: 15000, // IDR
    maxOrderQuantity: 50
  }
}
```

### Database Migration
```bash
# Generate Prisma client
npm run db:generate

# Push schema to database
npm run db:push

# Seed initial data
npm run db:seed
```

## Testing dan Quality Assurance

### Manual Testing Checklist
#### Customer Flow
- [ ] Add products to cart
- [ ] Navigate to checkout
- [ ] Select delivery location on map
- [ ] Choose payment method (QRIS/COD)
- [ ] Upload payment proof (QRIS)
- [ ] Submit order successfully
- [ ] View order in orders page
- [ ] Confirm delivery when shipped

#### Admin Flow
- [ ] Login to admin dashboard
- [ ] View new orders
- [ ] Verify payment proof (QRIS)
- [ ] Update order status to verified
- [ ] Ship order
- [ ] Handle order cancellation

### Error Scenarios
- [ ] Out of stock products
- [ ] Invalid delivery location
- [ ] Failed payment proof upload
- [ ] Network connectivity issues
- [ ] Session timeout handling

## Deployment Checklist

### Pre-deployment
- [ ] Environment variables configured
- [ ] Database schema migrated
- [ ] Static assets optimized
- [ ] Error logging configured
- [ ] Performance monitoring setup

### Post-deployment
- [ ] Health check endpoints working
- [ ] Database connections stable
- [ ] File upload permissions correct
- [ ] Email notifications working
- [ ] Payment integration tested

## Maintenance dan Monitoring

### Regular Tasks
1. **Database Maintenance**
   - Monitor query performance
   - Clean up old payment proofs
   - Archive completed orders
   - Update delivery zones if needed

2. **File Management**
   - Clean up unused uploaded files
   - Monitor storage usage
   - Backup payment proofs
   - Optimize image sizes

3. **Performance Monitoring**
   - Track API response times
   - Monitor database connections
   - Check error rates
   - Analyze user behavior

### Key Metrics to Monitor
- Order completion rate
- Payment verification time
- Average delivery time
- Customer satisfaction score
- System uptime
- API error rates

---

**Dokumentasi ini memberikan gambaran lengkap alur transaksi dari checkout hingga delivery confirmation, mencakup semua aspek teknis dan business logic yang terlibat dalam sistem e-commerce Acikoo.**

**Terakhir diupdate**: Januari 2024
**Versi**: 1.0
**Maintainer**: Development Team
