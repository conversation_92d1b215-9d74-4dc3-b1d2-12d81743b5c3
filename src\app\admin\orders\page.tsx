'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import {
  Package,
  Search,
  Filter,
  Eye,
  CheckCircle,
  Truck,
  X,
  Clock,
  CreditCard,
  MapPin,
  User,
  Phone
} from 'lucide-react'
import { OrderWithDetails } from '@/types'
import { toast } from 'sonner'
import Image from 'next/image'
import { formatCurrency, getDeliveryZoneInfo } from '@/lib/delivery-pricing'
import { getProductImage as getProductImageUtil } from '@/lib/product-images'

// Status configuration
const statusConfig = {
  PENDING_PAYMENT: {
    label: 'Menunggu Pembayaran',
    color: 'bg-yellow-500',
    textColor: 'text-yellow-700',
    bgColor: 'bg-yellow-50',
    icon: Clock
  },
  PAYMENT_VERIFIED: {
    label: 'Pembayaran Diverifikasi',
    color: 'bg-blue-500',
    textColor: 'text-blue-700',
    bgColor: 'bg-blue-50',
    icon: CheckCircle
  },
  SHIPPED: {
    label: 'Dikirim',
    color: 'bg-purple-500',
    textColor: 'text-purple-700',
    bgColor: 'bg-purple-50',
    icon: Truck
  },
  DELIVERED: {
    label: 'Selesai',
    color: 'bg-green-500',
    textColor: 'text-green-700',
    bgColor: 'bg-green-50',
    icon: CheckCircle
  },
  CANCELLED: {
    label: 'Dibatalkan',
    color: 'bg-red-500',
    textColor: 'text-red-700',
    bgColor: 'bg-red-50',
    icon: X
  }
}

function OrderCard({ order, onStatusUpdate }: {
  order: OrderWithDetails
  onStatusUpdate: () => void
}) {
  const [loading, setLoading] = useState(false)
  const [showDetails, setShowDetails] = useState(false)
  const [showPaymentProof, setShowPaymentProof] = useState(false)

  const status = statusConfig[order.status]
  const StatusIcon = status.icon

  const handleStatusUpdate = async (action: string) => {
    const confirmMessages = {
      verify_payment: 'Verifikasi pembayaran pesanan ini?',
      ship_order: 'Kirim pesanan ini?',
      cancel_order: 'Batalkan pesanan ini?'
    }

    if (!confirm(confirmMessages[action as keyof typeof confirmMessages])) return

    setLoading(true)
    try {
      const response = await fetch(`/api/admin/orders/${order.id}`, {
        method: 'PATCH',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ action })
      })

      if (!response.ok) {
        throw new Error('Gagal update status pesanan')
      }

      toast.success('Status pesanan berhasil diupdate!')
      onStatusUpdate()
    } catch (error) {
      toast.error('Gagal update status pesanan')
    } finally {
      setLoading(false)
    }
  }

  // Use centralized image utility
  const getProductImage = (productName: string) => {
    return getProductImageUtil(productName)
  }

  return (
    <Card className="mb-4">
      <CardHeader>
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <div className={`w-3 h-3 rounded-full ${status.color}`} />
            <div>
              <CardTitle className="text-lg">{order.orderNumber}</CardTitle>
              <div className="flex items-center space-x-4 text-sm text-gray-600">
                <span className="flex items-center">
                  <User className="w-4 h-4 mr-1" />
                  {order.user.name}
                </span>
                {order.user.phone && (
                  <span className="flex items-center">
                    <Phone className="w-4 h-4 mr-1" />
                    {order.user.phone}
                  </span>
                )}
                <span>
                  {new Date(order.createdAt).toLocaleDateString('id-ID', {
                    day: 'numeric',
                    month: 'short',
                    year: 'numeric',
                    hour: '2-digit',
                    minute: '2-digit'
                  })}
                </span>
              </div>
            </div>
          </div>

          <div className="flex items-center space-x-2">
            <Badge className={`${status.bgColor} ${status.textColor} border-0`}>
              <StatusIcon className="w-3 h-3 mr-1" />
              {status.label}
            </Badge>
            <Badge variant="outline">
              {order.paymentMethod === 'QRIS' ? '💳 QRIS' :
               order.paymentMethod === 'MIDTRANS' ? '💎 Midtrans' : '💰 COD'}
            </Badge>
            <Badge variant="secondary">
              {formatCurrency(Number(order.total))}
            </Badge>
          </div>
        </div>
      </CardHeader>

      <CardContent>
        {/* Order Items Preview */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
          <div>
            <h4 className="font-medium text-sm mb-2">Produk ({order.orderItems.length} item)</h4>
            <div className="space-y-2">
              {order.orderItems.slice(0, 2).map((item) => (
                <div key={item.id} className="flex items-center space-x-2 text-sm">
                  <div className="w-8 h-8 rounded overflow-hidden bg-gray-100">
                    <Image
                      src={getProductImage(item.product.name)}
                      alt={item.product.name}
                      width={32}
                      height={32}
                      className="w-full h-full object-cover"
                    />
                  </div>
                  <span className="flex-1">{item.product.name}</span>
                  <span className="text-gray-600">{item.quantity}x</span>
                  <span className="font-medium">{formatCurrency(Number(item.subtotal))}</span>
                </div>
              ))}
              {order.orderItems.length > 2 && (
                <p className="text-xs text-gray-500">+{order.orderItems.length - 2} produk lainnya</p>
              )}
            </div>
          </div>

          <div>
            <h4 className="font-medium text-sm mb-2 flex items-center">
              <MapPin className="w-4 h-4 mr-1" />
              Alamat Pengiriman
            </h4>
            <p className="text-sm text-gray-600 bg-gray-50 p-2 rounded">
              {order.deliveryAddress}
            </p>

            {/* Zone Info */}
            {order.deliveryLat && order.deliveryLng && (
              <div className="mt-2">
                {(() => {
                  const zoneInfo = getDeliveryZoneInfo(order.deliveryLat, order.deliveryLng)
                  return (
                    <div className="flex items-center space-x-2 text-xs">
                      <Badge variant={zoneInfo.isDeliverable ? "default" : "destructive"}>
                        {zoneInfo.zoneName}
                      </Badge>
                      <span className="text-gray-500">
                        Jarak: {zoneInfo.distance.toFixed(1)} km
                      </span>
                      <span className="font-medium text-red-600">
                        {formatCurrency(zoneInfo.deliveryFee)}
                      </span>
                    </div>
                  )
                })()}
              </div>
            )}

            {order.notes && (
              <p className="text-xs text-gray-500 mt-1">
                <strong>Catatan:</strong> {order.notes}
              </p>
            )}
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex flex-wrap gap-2">
          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowDetails(!showDetails)}
          >
            <Eye className="w-4 h-4 mr-2" />
            {showDetails ? 'Tutup' : 'Detail'}
          </Button>

          {/* QRIS Payment Actions */}
          {order.paymentMethod === 'QRIS' && (
            <>
              {order.payment?.paymentProof && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowPaymentProof(true)}
                >
                  <CreditCard className="w-4 h-4 mr-2" />
                  Lihat Bukti Bayar
                </Button>
              )}

              {order.status === 'PENDING_PAYMENT' && order.payment?.paymentProof && (
                <Button
                  size="sm"
                  onClick={() => handleStatusUpdate('verify_payment')}
                  disabled={loading}
                  className="bg-blue-600 hover:bg-blue-700"
                >
                  <CheckCircle className="w-4 h-4 mr-2" />
                  Verifikasi Pembayaran
                </Button>
              )}
            </>
          )}

          {/* COD Actions */}
          {order.paymentMethod === 'COD' && order.status === 'PENDING_PAYMENT' && (
            <Button
              size="sm"
              onClick={() => handleStatusUpdate('verify_payment')}
              disabled={loading}
              className="bg-blue-600 hover:bg-blue-700"
            >
              <CheckCircle className="w-4 h-4 mr-2" />
              Konfirmasi COD
            </Button>
          )}

          {/* Midtrans Actions */}
          {order.paymentMethod === 'MIDTRANS' && order.status === 'PENDING_PAYMENT' && (
            <Button
              size="sm"
              onClick={() => handleStatusUpdate('verify_payment')}
              disabled={loading}
              className="bg-purple-600 hover:bg-purple-700"
            >
              <CheckCircle className="w-4 h-4 mr-2" />
              Konfirmasi Midtrans
            </Button>
          )}

          {/* Ship Order */}
          {order.status === 'PAYMENT_VERIFIED' && (
            <Button
              size="sm"
              onClick={() => handleStatusUpdate('ship_order')}
              disabled={loading}
              className="bg-purple-600 hover:bg-purple-700"
            >
              <Truck className="w-4 h-4 mr-2" />
              Kirim Pesanan
            </Button>
          )}

          {/* Cancel Order */}
          {['PENDING_PAYMENT', 'PAYMENT_VERIFIED'].includes(order.status) && (
            <Button
              variant="destructive"
              size="sm"
              onClick={() => handleStatusUpdate('cancel_order')}
              disabled={loading}
            >
              <X className="w-4 h-4 mr-2" />
              Batalkan
            </Button>
          )}
        </div>

        {/* Detailed View */}
        {showDetails && (
          <div className="mt-4 pt-4 border-t space-y-4">
            <div>
              <h4 className="font-medium text-sm mb-2">Detail Produk</h4>
              <div className="space-y-2">
                {order.orderItems.map((item) => (
                  <div key={item.id} className="flex items-center justify-between p-2 bg-gray-50 rounded">
                    <div className="flex items-center space-x-3">
                      <div className="w-12 h-12 rounded overflow-hidden bg-white">
                        <Image
                          src={getProductImage(item.product.name)}
                          alt={item.product.name}
                          width={48}
                          height={48}
                          className="w-full h-full object-cover"
                        />
                      </div>
                      <div>
                        <p className="font-medium text-sm">{item.product.name}</p>
                        <p className="text-xs text-gray-600">
                          {formatCurrency(Number(item.price))} x {item.quantity}
                        </p>
                      </div>
                    </div>
                    <p className="font-medium">{formatCurrency(Number(item.subtotal))}</p>
                  </div>
                ))}
              </div>
            </div>

            <div>
              <h4 className="font-medium text-sm mb-2">Ringkasan Pembayaran</h4>
              <div className="bg-gray-50 p-3 rounded space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Subtotal</span>
                  <span>{formatCurrency(Number(order.subtotal))}</span>
                </div>
                <div className="flex justify-between text-sm">
                  <span>Ongkos Kirim</span>
                  <span>{formatCurrency(Number(order.deliveryFee))}</span>
                </div>
                <div className="border-t pt-2">
                  <div className="flex justify-between font-medium">
                    <span>Total</span>
                    <span className="text-red-600">{formatCurrency(Number(order.total))}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Payment Proof Modal */}
        {showPaymentProof && order.payment?.paymentProof && (
          <>
            <div
              className="fixed inset-0 bg-black/50 z-40"
              onClick={() => setShowPaymentProof(false)}
            />
            <div className="fixed inset-0 flex items-center justify-center z-50 p-4">
              <div className="bg-white rounded-lg p-6 max-w-md w-full">
                <h3 className="text-lg font-semibold mb-4">Bukti Pembayaran</h3>
                <div className="mb-4">
                  <Image
                    src={order.payment.paymentProof}
                    alt="Bukti Pembayaran"
                    width={400}
                    height={300}
                    className="w-full h-auto rounded-lg border"
                  />
                </div>
                <div className="flex space-x-2">
                  <Button
                    onClick={() => setShowPaymentProof(false)}
                    variant="outline"
                    className="flex-1"
                  >
                    Tutup
                  </Button>
                  {order.status === 'PENDING_PAYMENT' && (
                    <Button
                      onClick={() => {
                        setShowPaymentProof(false)
                        handleStatusUpdate('verify_payment')
                      }}
                      disabled={loading}
                      className="flex-1 bg-blue-600 hover:bg-blue-700"
                    >
                      Verifikasi
                    </Button>
                  )}
                </div>
              </div>
            </div>
          </>
        )}
      </CardContent>
    </Card>
  )
}

export default function AdminOrdersPage() {
  const [orders, setOrders] = useState<OrderWithDetails[]>([])
  const [customers, setCustomers] = useState<{ id: string; name: string; email: string }[]>([])
  const [loading, setLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [statusFilter, setStatusFilter] = useState('all')
  const [paymentFilter, setPaymentFilter] = useState('all')
  const [customerFilter, setCustomerFilter] = useState('all')
  const [dateFilter, setDateFilter] = useState('all')
  const [activeTab, setActiveTab] = useState<'ongoing' | 'completed'>('ongoing')

  useEffect(() => {
    fetchOrders()
    fetchCustomers()
  }, [statusFilter, paymentFilter, customerFilter, dateFilter])

  const fetchCustomers = async () => {
    try {
      const response = await fetch('/api/admin/customers')
      const result = await response.json()
      if (result.success && result.data.customers && Array.isArray(result.data.customers)) {
        setCustomers(result.data.customers)
      } else {
        console.warn('Customers data is not an array:', result.data)
        setCustomers([]) // Fallback to empty array
      }
    } catch (error) {
      console.error('Error fetching customers:', error)
      setCustomers([]) // Fallback to empty array on error
    }
  }

  const fetchOrders = async () => {
    try {
      const params = new URLSearchParams()
      if (statusFilter !== 'all') params.append('status', statusFilter)
      if (paymentFilter !== 'all') params.append('paymentMethod', paymentFilter)
      if (customerFilter !== 'all') params.append('customerId', customerFilter)
      if (dateFilter !== 'all') params.append('dateRange', dateFilter)

      const response = await fetch(`/api/admin/orders?${params.toString()}`)
      const result = await response.json()

      if (result.success) {
        setOrders(result.data)
      } else {
        toast.error('Gagal memuat pesanan')
      }
    } catch (error) {
      toast.error('Gagal memuat pesanan')
    } finally {
      setLoading(false)
    }
  }

  // Separate orders by status
  const ongoingStatuses = ['PENDING_PAYMENT', 'PAYMENT_VERIFIED', 'SHIPPED']
  const completedStatuses = ['DELIVERED', 'CANCELLED']

  const ongoingOrders = orders.filter(order => ongoingStatuses.includes(order.status))
  const completedOrders = orders.filter(order => completedStatuses.includes(order.status))

  const currentOrders = activeTab === 'ongoing' ? ongoingOrders : completedOrders

  const filteredOrders = currentOrders.filter(order => {
    if (!searchQuery) return true

    const query = searchQuery.toLowerCase()
    return (
      order.orderNumber.toLowerCase().includes(query) ||
      order.user.name.toLowerCase().includes(query) ||
      order.user.email.toLowerCase().includes(query) ||
      order.orderItems.some(item =>
        item.product.name.toLowerCase().includes(query)
      )
    )
  })

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-2xl font-bold text-gray-900">Pesanan</h1>
          <p className="text-gray-600">Kelola pesanan dan data pelanggan dalam satu tempat</p>
        </div>
        <div className="flex items-center space-x-2">
          <Badge variant="secondary">
            {ongoingOrders.length} Ongoing
          </Badge>
          <Badge variant="outline">
            {completedOrders.length} Selesai
          </Badge>
        </div>
      </div>

      {/* Tab Navigation */}
      <div className="border-b border-gray-200">
        <nav className="-mb-px flex space-x-8">
          <button
            onClick={() => setActiveTab('ongoing')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'ongoing'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            Pesanan Masuk & Ongoing
            {ongoingOrders.length > 0 && (
              <Badge variant="secondary" className="ml-2">
                {ongoingOrders.length}
              </Badge>
            )}
          </button>
          <button
            onClick={() => setActiveTab('completed')}
            className={`py-2 px-1 border-b-2 font-medium text-sm ${
              activeTab === 'completed'
                ? 'border-blue-500 text-blue-600'
                : 'border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300'
            }`}
          >
            Riwayat Pesanan
            {completedOrders.length > 0 && (
              <Badge variant="outline" className="ml-2">
                {completedOrders.length}
              </Badge>
            )}
          </button>
        </nav>
      </div>

      {/* Enhanced Filters */}
      <Card>
        <CardContent className="p-4">
          <div className="space-y-4">
            {/* Search Bar */}
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <Input
                placeholder="Cari pesanan, pelanggan, produk..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                className="pl-10"
              />
            </div>

            {/* Filter Row */}
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {/* Customer Filter */}
              <Select value={customerFilter} onValueChange={setCustomerFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="Filter Customer" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Semua Customer</SelectItem>
                  {Array.isArray(customers) && customers.map((customer) => (
                    <SelectItem key={customer.id} value={customer.id}>
                      {customer.name || customer.email}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>

              {/* Status Filter - Conditional based on active tab */}
              <Select value={statusFilter} onValueChange={setStatusFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="Filter Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Semua Status</SelectItem>
                  {activeTab === 'ongoing' ? (
                    <>
                      <SelectItem value="PENDING_PAYMENT">Menunggu Pembayaran</SelectItem>
                      <SelectItem value="PAYMENT_VERIFIED">Pembayaran Diverifikasi</SelectItem>
                      <SelectItem value="SHIPPED">Dikirim</SelectItem>
                    </>
                  ) : (
                    <>
                      <SelectItem value="DELIVERED">Selesai</SelectItem>
                      <SelectItem value="CANCELLED">Dibatalkan</SelectItem>
                    </>
                  )}
                </SelectContent>
              </Select>

              {/* Payment Method Filter */}
              <Select value={paymentFilter} onValueChange={setPaymentFilter}>
                <SelectTrigger>
                  <SelectValue placeholder="Filter Pembayaran" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Semua Metode</SelectItem>
                  <SelectItem value="QRIS">QRIS</SelectItem>
                  <SelectItem value="COD">Cash on Delivery</SelectItem>
                </SelectContent>
              </Select>

              {/* Date Filter - Only for completed tab */}
              {activeTab === 'completed' && (
                <Select value={dateFilter} onValueChange={setDateFilter}>
                  <SelectTrigger>
                    <SelectValue placeholder="Filter Waktu" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Semua Waktu</SelectItem>
                    <SelectItem value="today">Hari Ini</SelectItem>
                    <SelectItem value="week">7 Hari Terakhir</SelectItem>
                    <SelectItem value="month">30 Hari Terakhir</SelectItem>
                    <SelectItem value="3months">3 Bulan Terakhir</SelectItem>
                  </SelectContent>
                </Select>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Orders List */}
      {loading ? (
        <div className="space-y-4">
          {[1, 2, 3].map((i) => (
            <Card key={i} className="animate-pulse">
              <CardContent className="p-6">
                <div className="h-4 bg-gray-200 rounded w-1/4 mb-4"></div>
                <div className="h-3 bg-gray-200 rounded w-1/2 mb-2"></div>
                <div className="h-3 bg-gray-200 rounded w-1/3"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : filteredOrders.length === 0 ? (
        <Card>
          <CardContent className="p-8 text-center">
            <Package className="w-16 h-16 text-gray-300 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 mb-2">
              {searchQuery || statusFilter !== 'all' || paymentFilter !== 'all'
                ? 'Tidak Ada Pesanan Ditemukan'
                : 'Belum Ada Pesanan'
              }
            </h3>
            <p className="text-gray-600">
              {searchQuery || statusFilter !== 'all' || paymentFilter !== 'all'
                ? 'Coba ubah filter pencarian Anda'
                : 'Pesanan akan muncul di sini ketika pelanggan mulai berbelanja'
              }
            </p>
          </CardContent>
        </Card>
      ) : (
        <div>
          <div className="mb-4 text-sm text-gray-600">
            Menampilkan {filteredOrders.length} pesanan
          </div>
          {filteredOrders.map((order) => (
            <OrderCard
              key={order.id}
              order={order}
              onStatusUpdate={fetchOrders}
            />
          ))}
        </div>
      )}
    </div>
  )
}
