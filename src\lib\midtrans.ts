import { Core<PERSON><PERSON>, Snap } from 'midtrans-client'
import crypto from 'crypto'

// Midtrans configuration
const isProduction = process.env.NODE_ENV === 'production'

export const snap = new Snap({
  isProduction,
  serverKey: process.env.MIDTRANS_SERVER_KEY!,
  clientKey: process.env.MIDTRANS_CLIENT_KEY!,
})

export const coreApi = new CoreApi({
  isProduction,
  serverKey: process.env.MIDTRANS_SERVER_KEY!,
  clientKey: process.env.MIDTRANS_CLIENT_KEY!,
})

// Midtrans configuration constants
export const MIDTRANS_CONFIG = {
  serverKey: process.env.MIDTRANS_SERVER_KEY!,
  clientKey: process.env.MIDTRANS_CLIENT_KEY!,
  merchantId: process.env.MIDTRANS_MERCHANT_ID!,
  isProduction,
  snapUrl: isProduction 
    ? 'https://app.midtrans.com/snap/snap.js'
    : 'https://app.sandbox.midtrans.com/snap/snap.js'
}

// Create Midtrans transaction parameter
export interface MidtransTransactionParams {
  orderId: string
  grossAmount: number
  customerDetails: {
    first_name: string
    email: string
    phone?: string
  }
  itemDetails: Array<{
    id: string
    price: number
    quantity: number
    name: string
  }>
}

export function createTransactionParams(params: MidtransTransactionParams) {
  return {
    transaction_details: {
      order_id: params.orderId,
      gross_amount: params.grossAmount,
    },
    customer_details: params.customerDetails,
    item_details: params.itemDetails,
    credit_card: {
      secure: true,
    },
    callbacks: {
      finish: `${process.env.NEXTAUTH_URL}/payment/finish`,
      error: `${process.env.NEXTAUTH_URL}/payment/error`,
      pending: `${process.env.NEXTAUTH_URL}/payment/pending`,
    },
  }
}

// Validate Midtrans signature
export function validateSignature(
  orderId: string,
  statusCode: string,
  grossAmount: string,
  signatureKey: string
): boolean {
  const serverKey = MIDTRANS_CONFIG.serverKey
  const input = orderId + statusCode + grossAmount + serverKey
  const hash = crypto.createHash('sha512').update(input).digest('hex')
  
  return hash === signatureKey
}

// Map Midtrans status to our payment status
export function mapMidtransStatus(transactionStatus: string, fraudStatus?: string): string {
  switch (transactionStatus) {
    case 'capture':
      return fraudStatus === 'challenge' ? 'PENDING' : 'SETTLEMENT'
    case 'settlement':
      return 'SETTLEMENT'
    case 'pending':
      return 'PENDING'
    case 'deny':
      return 'DENY'
    case 'cancel':
      return 'CANCEL'
    case 'expire':
      return 'EXPIRE'
    case 'failure':
      return 'FAILED'
    default:
      return 'PENDING'
  }
}

// Generate unique order ID for Midtrans
export function generateMidtransOrderId(orderNumber: string): string {
  const timestamp = Date.now()
  return `${orderNumber}-${timestamp}`
}

// Midtrans notification interface
export interface MidtransNotification {
  transaction_time: string
  transaction_status: string
  transaction_id: string
  status_message: string
  status_code: string
  signature_key: string
  payment_type: string
  order_id: string
  merchant_id: string
  masked_card?: string
  gross_amount: string
  fraud_status?: string
  eci?: string
  currency: string
  channel_response_message?: string
  channel_response_code?: string
  card_type?: string
  bank?: string
  biller_code?: string
  bill_key?: string
  va_numbers?: Array<{
    va_number: string
    bank: string
  }>
  permata_va_number?: string
  pdf_url?: string
  finish_redirect_url?: string
}

// Error handling for Midtrans API
export class MidtransError extends Error {
  constructor(
    message: string,
    public statusCode?: number,
    public response?: any
  ) {
    super(message)
    this.name = 'MidtransError'
  }
}

// Helper function to handle Midtrans API errors
export function handleMidtransError(error: any): never {
  if (error.httpStatusCode) {
    throw new MidtransError(
      error.ApiResponse?.status_message || error.message,
      error.httpStatusCode,
      error.ApiResponse
    )
  }
  throw new MidtransError(error.message || 'Unknown Midtrans error')
}
