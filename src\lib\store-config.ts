// Centralized store configuration
export const STORE_CONFIG = {
  // Store Information
  name: '<PERSON><PERSON><PERSON><PERSON>',
  tagline: '<PERSON><PERSON>!',
  description: 'Spesialis makanan ringan berbahan dasar aci dengan cita rasa pedas yang menggugah selera',

  // Location - Corrected coordinates for Jl. Tanah Merah, Pluit
  location: {
    address: 'Jl. Tanah Merah No.15, RT.15/RW.8, Pluit, Kec. Penjaringan, Jakarta Utara, DKI Jakarta 14440',
    latitude: -6.1275,
    longitude: 106.7906,
    mapsUrl: 'https://www.openstreetmap.org/?mlat=-6.1275&mlon=106.7906&zoom=16',
    googleMapsUrl: 'https://maps.google.com/?q=-6.1275,106.7906',
    landmark: 'Pluit, Jakarta Utara'
  },

  // Contact Information
  contact: {
    phone: '+62 812-9586-8699',
    whatsapp: '+6281295868699',
    email: '<EMAIL>',
    instagram: '@acikoo_official',
    facebook: 'Acikoo Official'
  },

  // Operating Hours
  operatingHours: {
    monday: { open: '08:00', close: '22:00', isOpen: true },
    tuesday: { open: '08:00', close: '22:00', isOpen: true },
    wednesday: { open: '08:00', close: '22:00', isOpen: true },
    thursday: { open: '08:00', close: '22:00', isOpen: true },
    friday: { open: '08:00', close: '22:00', isOpen: true },
    saturday: { open: '08:00', close: '23:00', isOpen: true },
    sunday: { open: '09:00', close: '21:00', isOpen: true }
  },

  // Delivery Configuration
  delivery: {
    zones: [
      {
        name: 'Zone 1 (0-4km)',
        maxDistance: 4,
        fee: 10000,
        description: 'Area terdekat dari toko',
        color: '#22c55e'
      },
      {
        name: 'Zone 2 (5km)',
        maxDistance: 5,
        fee: 13000,
        description: 'Area Jakarta Utara',
        color: '#3b82f6'
      },
      {
        name: 'Zone 3 (6km)',
        maxDistance: 6,
        fee: 16000,
        description: 'Area Jakarta Pusat',
        color: '#f59e0b'
      },
      {
        name: 'Zone 4 (7km)',
        maxDistance: 7,
        fee: 19000,
        description: 'Area Jakarta Barat',
        color: '#f97316'
      },
      {
        name: 'Zone 5 (8km)',
        maxDistance: 8,
        fee: 22000,
        description: 'Area Jakarta Selatan',
        color: '#ef4444'
      },
      {
        name: 'Zone 6 (9km)',
        maxDistance: 9,
        fee: 25000,
        description: 'Area Jakarta & sekitarnya',
        color: '#dc2626'
      }
    ],
    freeDeliveryThreshold: 50000, // Free delivery for orders above 50k in zone 1
    maxDeliveryDistance: 9, // Maximum delivery distance in km
    estimatedTime: {
      preparation: 15, // minutes
      baseDelivery: 30, // minutes
      perKm: 8 // additional minutes per km
    }
  },

  // Business Settings
  business: {
    currency: 'IDR',
    taxRate: 0, // No tax for now
    serviceCharge: 0, // No service charge
    minimumOrder: 15000, // Minimum order amount
    maxOrderQuantity: 50 // Maximum quantity per item
  },

  // Payment Methods
  paymentMethods: [
    {
      id: 'qris',
      name: 'QRIS',
      description: 'Scan QR Code dengan aplikasi e-wallet',
      isActive: true,
      icon: '📱'
    },
    {
      id: 'cod',
      name: 'Cash on Delivery',
      description: 'Bayar tunai saat pesanan tiba',
      isActive: true,
      icon: '💵'
    },
    {
      id: 'midtrans',
      name: 'Midtrans',
      description: 'Pembayaran digital dengan berbagai metode',
      isActive: true,
      icon: '💎'
    }
  ],

  // Social Media
  socialMedia: {
    instagram: {
      url: 'https://instagram.com/acikoo_official',
      handle: '@acikoo_official'
    },
    facebook: {
      url: 'https://facebook.com/acikoo.official',
      handle: 'Acikoo Official'
    },
    tiktok: {
      url: 'https://tiktok.com/@acikoo_official',
      handle: '@acikoo_official'
    },
    youtube: {
      url: 'https://youtube.com/@acikoo',
      handle: 'Acikoo Channel'
    }
  }
}

// Helper functions
export const getStoreLocation = () => STORE_CONFIG.location

export const getDeliveryZones = () => STORE_CONFIG.delivery.zones

export const getDeliveryFee = (distance: number): number => {
  const zone = STORE_CONFIG.delivery.zones.find(z => distance <= z.maxDistance)
  return zone ? zone.fee : 0
}

export const isDeliverable = (distance: number): boolean => {
  return distance <= STORE_CONFIG.delivery.maxDeliveryDistance
}

export const getFreeDeliveryThreshold = () => STORE_CONFIG.delivery.freeDeliveryThreshold

export const isStoreOpen = (): boolean => {
  const now = new Date()
  const days = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday']
  const day = days[now.getDay()] as keyof typeof STORE_CONFIG.operatingHours
  const currentTime = now.toTimeString().slice(0, 5) // HH:MM format

  const todayHours = STORE_CONFIG.operatingHours[day]
  if (!todayHours.isOpen) return false

  return currentTime >= todayHours.open && currentTime <= todayHours.close
}

export const getOperatingHours = (day?: string) => {
  if (day) {
    return STORE_CONFIG.operatingHours[day.toLowerCase() as keyof typeof STORE_CONFIG.operatingHours]
  }
  return STORE_CONFIG.operatingHours
}

export const formatOperatingHours = (hours: typeof STORE_CONFIG.operatingHours[keyof typeof STORE_CONFIG.operatingHours]) => {
  if (!hours.isOpen) return 'Tutup'
  return `${hours.open} - ${hours.close}`
}

export const getContactInfo = () => STORE_CONFIG.contact

export const getBusinessSettings = () => STORE_CONFIG.business

export const getPaymentMethods = () => STORE_CONFIG.paymentMethods.filter(method => method.isActive)

export const getSocialMedia = () => STORE_CONFIG.socialMedia

// Calculate distance between two coordinates using Haversine formula
export const calculateDistance = (lat1: number, lon1: number, lat2: number, lon2: number): number => {
  const R = 6371 // Earth's radius in kilometers
  const dLat = (lat2 - lat1) * Math.PI / 180
  const dLon = (lon2 - lon1) * Math.PI / 180
  const a =
    Math.sin(dLat/2) * Math.sin(dLat/2) +
    Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) *
    Math.sin(dLon/2) * Math.sin(dLon/2)
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a))
  return R * c
}

// Calculate delivery info based on customer location
export const calculateDeliveryInfo = (customerLat: number, customerLon: number, subtotal: number = 0) => {
  const storeLocation = getStoreLocation()
  const distance = calculateDistance(
    storeLocation.latitude,
    storeLocation.longitude,
    customerLat,
    customerLon
  )

  const zone = STORE_CONFIG.delivery.zones.find(z => distance <= z.maxDistance)
  const isDeliverableLocation = isDeliverable(distance)
  const deliveryFee = zone ? zone.fee : 0
  const isFreeDelivery = subtotal >= getFreeDeliveryThreshold() && distance <= 3
  const finalFee = isFreeDelivery ? 0 : deliveryFee

  // Calculate estimated delivery time
  const { preparation, baseDelivery, perKm } = STORE_CONFIG.delivery.estimatedTime
  const estimatedMinutes = preparation + baseDelivery + Math.floor(distance * perKm)
  const estimatedDeliveryTime = new Date(Date.now() + estimatedMinutes * 60 * 1000)

  return {
    distance: Math.round(distance * 100) / 100,
    zone,
    originalFee: deliveryFee,
    finalFee,
    isFreeDelivery,
    isDeliverable: isDeliverableLocation,
    estimatedMinutes,
    estimatedDeliveryTime,
    freeDeliveryThreshold: getFreeDeliveryThreshold()
  }
}
