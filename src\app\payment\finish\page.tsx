'use client'

import { useEffect, useState } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { useSession } from 'next-auth/react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { CheckCircle, Clock, XCircle, AlertCircle, ArrowLeft, ShoppingBag } from 'lucide-react'
import { toast } from 'sonner'

interface PaymentStatus {
  success: boolean
  paymentStatus: string
  orderStatus: string
  transactionId?: string
  paymentType?: string
  statusMessage?: string
  error?: string
}

const statusConfig = {
  SETTLEMENT: {
    icon: CheckCircle,
    color: 'text-green-600',
    bgColor: 'bg-green-50',
    title: 'Pembayaran Berhasil!',
    description: 'Pembayaran Anda telah berhasil diproses. Pesanan akan segera diproses.'
  },
  PENDING: {
    icon: Clock,
    color: 'text-yellow-600',
    bgColor: 'bg-yellow-50',
    title: 'Pembayaran Pending',
    description: 'Pembayaran Anda sedang diproses. Mohon tunggu konfirmasi.'
  },
  DENY: {
    icon: XCircle,
    color: 'text-red-600',
    bgColor: 'bg-red-50',
    title: 'Pembayaran Ditolak',
    description: 'Pembayaran Anda ditolak. Silakan coba lagi dengan metode pembayaran lain.'
  },
  CANCEL: {
    icon: XCircle,
    color: 'text-red-600',
    bgColor: 'bg-red-50',
    title: 'Pembayaran Dibatalkan',
    description: 'Pembayaran dibatalkan. Anda dapat mencoba lagi.'
  },
  EXPIRE: {
    icon: AlertCircle,
    color: 'text-orange-600',
    bgColor: 'bg-orange-50',
    title: 'Pembayaran Kedaluwarsa',
    description: 'Waktu pembayaran telah habis. Silakan buat pesanan baru.'
  },
  FAILED: {
    icon: XCircle,
    color: 'text-red-600',
    bgColor: 'bg-red-50',
    title: 'Pembayaran Gagal',
    description: 'Terjadi kesalahan saat memproses pembayaran. Silakan coba lagi.'
  }
}

export default function PaymentFinishPage() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const { data: session } = useSession()
  const [paymentStatus, setPaymentStatus] = useState<PaymentStatus | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    if (!session) {
      router.push('/auth/signin')
      return
    }

    const orderId = searchParams.get('order_id')
    const statusCode = searchParams.get('status_code')
    const transactionStatus = searchParams.get('transaction_status')

    if (!orderId) {
      toast.error('Order ID tidak ditemukan')
      router.push('/orders')
      return
    }

    // Check payment status from our API
    checkPaymentStatus(orderId)
  }, [session, searchParams, router])

  const checkPaymentStatus = async (orderId: string) => {
    try {
      // Extract order ID from Midtrans order ID format (ACK-XXXXXX-timestamp)
      const orderNumber = orderId.split('-').slice(0, -1).join('-')
      
      // Find order by order number
      const ordersResponse = await fetch('/api/orders')
      const ordersResult = await ordersResponse.json()
      
      if (!ordersResult.success) {
        throw new Error('Gagal mengambil data pesanan')
      }

      const order = ordersResult.data.find((o: any) => o.orderNumber === orderNumber)
      
      if (!order) {
        throw new Error('Pesanan tidak ditemukan')
      }

      // Check Midtrans payment status
      const statusResponse = await fetch('/api/payments/midtrans/status', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ orderId: order.id })
      })

      const statusResult = await statusResponse.json()

      if (statusResult.success) {
        setPaymentStatus({
          success: true,
          paymentStatus: statusResult.data.paymentStatus,
          orderStatus: statusResult.data.orderStatus,
          transactionId: statusResult.data.transactionId,
          paymentType: statusResult.data.paymentType,
          statusMessage: statusResult.data.statusMessage
        })
      } else {
        setPaymentStatus({
          success: false,
          paymentStatus: 'FAILED',
          orderStatus: 'CANCELLED',
          error: statusResult.error
        })
      }
    } catch (error) {
      console.error('Check payment status error:', error)
      setPaymentStatus({
        success: false,
        paymentStatus: 'FAILED',
        orderStatus: 'CANCELLED',
        error: error instanceof Error ? error.message : 'Terjadi kesalahan'
      })
    } finally {
      setLoading(false)
    }
  }

  if (!session) {
    return null
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <div className="w-12 h-12 border-4 border-red-600 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Mengecek Status Pembayaran</h2>
          <p className="text-gray-600">Mohon tunggu sebentar...</p>
        </div>
      </div>
    )
  }

  if (!paymentStatus) {
    return (
      <div className="min-h-screen bg-gray-50 flex items-center justify-center">
        <div className="text-center">
          <AlertCircle className="w-16 h-16 text-gray-400 mx-auto mb-4" />
          <h2 className="text-xl font-semibold text-gray-900 mb-2">Status Tidak Ditemukan</h2>
          <p className="text-gray-600 mb-4">Tidak dapat mengambil status pembayaran</p>
          <Button onClick={() => router.push('/orders')} className="bg-red-600 hover:bg-red-700">
            Lihat Pesanan
          </Button>
        </div>
      </div>
    )
  }

  const config = statusConfig[paymentStatus.paymentStatus as keyof typeof statusConfig] || statusConfig.FAILED
  const StatusIcon = config.icon

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="container mx-auto px-4 max-w-2xl">
        {/* Header */}
        <div className="flex items-center mb-6">
          <Button
            variant="ghost"
            onClick={() => router.push('/orders')}
            className="mr-4"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Kembali ke Pesanan
          </Button>
        </div>

        {/* Payment Status Card */}
        <Card>
          <CardHeader className="text-center">
            <div className={`w-16 h-16 rounded-full ${config.bgColor} flex items-center justify-center mx-auto mb-4`}>
              <StatusIcon className={`w-8 h-8 ${config.color}`} />
            </div>
            <CardTitle className="text-2xl">{config.title}</CardTitle>
            <p className="text-gray-600">{config.description}</p>
          </CardHeader>

          <CardContent className="space-y-4">
            {/* Payment Details */}
            {paymentStatus.transactionId && (
              <div className="bg-gray-50 p-4 rounded-lg space-y-2">
                <h4 className="font-medium text-sm">Detail Pembayaran</h4>
                <div className="text-sm space-y-1">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Transaction ID:</span>
                    <span className="font-mono">{paymentStatus.transactionId}</span>
                  </div>
                  {paymentStatus.paymentType && (
                    <div className="flex justify-between">
                      <span className="text-gray-600">Metode Pembayaran:</span>
                      <span className="capitalize">{paymentStatus.paymentType.replace('_', ' ')}</span>
                    </div>
                  )}
                  <div className="flex justify-between">
                    <span className="text-gray-600">Status Pesanan:</span>
                    <span className="capitalize">{paymentStatus.orderStatus.replace('_', ' ')}</span>
                  </div>
                </div>
              </div>
            )}

            {/* Error Message */}
            {paymentStatus.error && (
              <div className="bg-red-50 border border-red-200 p-4 rounded-lg">
                <p className="text-red-800 text-sm">{paymentStatus.error}</p>
              </div>
            )}

            {/* Action Buttons */}
            <div className="flex space-x-3 pt-4">
              <Button
                onClick={() => router.push('/orders')}
                className="flex-1 bg-red-600 hover:bg-red-700"
              >
                Lihat Pesanan Saya
              </Button>
              <Button
                variant="outline"
                onClick={() => router.push('/')}
                className="flex-1"
              >
                <ShoppingBag className="w-4 h-4 mr-2" />
                Belanja Lagi
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
