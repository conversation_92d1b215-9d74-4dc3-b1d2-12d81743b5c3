/**
 * Test script untuk Midtrans integration
 * Jalankan dengan: node scripts/test-midtrans.js
 */

// Load environment variables
require('dotenv').config({ path: '.env.local' })

const { PrismaClient } = require('@prisma/client')

const prisma = new PrismaClient()

async function testMidtransIntegration() {
  console.log('🧪 Testing Midtrans Integration...\n')

  try {
    // 1. Test database schema changes
    console.log('1. Testing database schema...')
    
    // Test PaymentMethod enum
    const testOrder = await prisma.order.findFirst({
      include: { payment: true }
    })
    
    if (testOrder) {
      console.log('✅ Database connection OK')
      console.log('✅ Order table accessible')
      console.log('✅ Payment table accessible')
    } else {
      console.log('⚠️  No orders found in database')
    }

    // 2. Test Midtrans configuration
    console.log('\n2. Testing Midtrans configuration...')
    
    const requiredEnvVars = [
      'MIDTRANS_SERVER_KEY',
      'MIDTRANS_CLIENT_KEY', 
      'MIDTRANS_MERCHANT_ID',
      'NEXTAUTH_URL'
    ]

    let configOK = true
    requiredEnvVars.forEach(envVar => {
      if (process.env[envVar]) {
        console.log(`✅ ${envVar} is set`)
      } else {
        console.log(`❌ ${envVar} is missing`)
        configOK = false
      }
    })

    if (configOK) {
      console.log('✅ All Midtrans environment variables are configured')
    } else {
      console.log('❌ Some environment variables are missing')
    }

    // 3. Test Midtrans SDK
    console.log('\n3. Testing Midtrans SDK...')
    
    try {
      const { Snap } = require('midtrans-client')
      
      const snap = new Snap({
        isProduction: false,
        serverKey: process.env.MIDTRANS_SERVER_KEY,
        clientKey: process.env.MIDTRANS_CLIENT_KEY,
      })

      console.log('✅ Midtrans SDK imported successfully')
      console.log('✅ Snap client initialized')
      
      // Test create transaction (dry run)
      const testTransactionParams = {
        transaction_details: {
          order_id: 'TEST-' + Date.now(),
          gross_amount: 50000,
        },
        customer_details: {
          first_name: 'Test Customer',
          email: '<EMAIL>',
        },
        item_details: [
          {
            id: 'test-item',
            price: 45000,
            quantity: 1,
            name: 'Test Product',
          },
          {
            id: 'delivery-fee',
            price: 5000,
            quantity: 1,
            name: 'Ongkos Kirim',
          },
        ],
      }

      console.log('✅ Transaction parameters prepared')
      console.log('📝 Test transaction params:', JSON.stringify(testTransactionParams, null, 2))

    } catch (error) {
      console.log('❌ Midtrans SDK error:', error.message)
    }

    // 4. Test API endpoints (mock)
    console.log('\n4. Testing API endpoint structure...')
    
    const fs = require('fs')
    const path = require('path')
    
    const apiEndpoints = [
      'src/app/api/payments/midtrans/create/route.ts',
      'src/app/api/payments/midtrans/notification/route.ts', 
      'src/app/api/payments/midtrans/status/route.ts'
    ]

    apiEndpoints.forEach(endpoint => {
      const fullPath = path.join(process.cwd(), endpoint)
      if (fs.existsSync(fullPath)) {
        console.log(`✅ ${endpoint} exists`)
      } else {
        console.log(`❌ ${endpoint} missing`)
      }
    })

    // 5. Test frontend pages
    console.log('\n5. Testing frontend pages...')
    
    const frontendPages = [
      'src/app/payment/finish/page.tsx',
      'src/app/payment/error/page.tsx',
      'src/app/payment/pending/page.tsx'
    ]

    frontendPages.forEach(page => {
      const fullPath = path.join(process.cwd(), page)
      if (fs.existsSync(fullPath)) {
        console.log(`✅ ${page} exists`)
      } else {
        console.log(`❌ ${page} missing`)
      }
    })

    // 6. Test signature validation
    console.log('\n6. Testing signature validation...')
    
    const crypto = require('crypto')
    
    const testOrderId = 'TEST-123456'
    const testStatusCode = '200'
    const testGrossAmount = '50000.00'
    const serverKey = process.env.MIDTRANS_SERVER_KEY
    
    if (serverKey) {
      const input = testOrderId + testStatusCode + testGrossAmount + serverKey
      const hash = crypto.createHash('sha512').update(input).digest('hex')
      
      console.log('✅ Signature validation function works')
      console.log('📝 Test signature:', hash.substring(0, 20) + '...')
    } else {
      console.log('❌ Cannot test signature validation - server key missing')
    }

    console.log('\n🎉 Midtrans integration test completed!')
    console.log('\n📋 Next steps:')
    console.log('1. Set up ngrok: npm install -g ngrok')
    console.log('2. Run ngrok: ngrok http 3000')
    console.log('3. Update Midtrans dashboard with ngrok URLs')
    console.log('4. Test with sandbox payment methods')
    console.log('5. Monitor webhook notifications')

  } catch (error) {
    console.error('❌ Test failed:', error)
  } finally {
    await prisma.$disconnect()
  }
}

// Run test if called directly
if (require.main === module) {
  testMidtransIntegration()
}

module.exports = { testMidtransIntegration }
