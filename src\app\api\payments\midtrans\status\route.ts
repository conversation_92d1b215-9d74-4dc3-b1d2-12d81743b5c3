import { NextRequest, NextResponse } from 'next/server'
import { getServerSession } from 'next-auth'
import { authOptions } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { coreApi, mapMidtransStatus, handleMidtransError } from '@/lib/midtrans'
import { z } from 'zod'

const checkStatusSchema = z.object({
  orderId: z.string(),
})

// POST /api/payments/midtrans/status - Check Midtrans payment status
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)
    if (!session?.user?.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 })
    }

    const body = await request.json()
    const { orderId } = checkStatusSchema.parse(body)

    // Get payment record
    const payment = await prisma.payment.findFirst({
      where: {
        order: {
          id: orderId,
          userId: session.user.id,
        },
      },
      include: {
        order: true,
      },
    })

    if (!payment) {
      return NextResponse.json({ error: 'Payment not found' }, { status: 404 })
    }

    if (!payment.midtransOrderId) {
      return NextResponse.json(
        { error: 'Midtrans order ID not found' },
        { status: 400 }
      )
    }

    // Check status from Midtrans
    const statusResponse = await coreApi.transaction.status(payment.midtransOrderId)

    // Map status
    const newPaymentStatus = mapMidtransStatus(
      statusResponse.transaction_status,
      statusResponse.fraud_status
    )

    // Update payment if status changed
    if (newPaymentStatus !== payment.status) {
      let newOrderStatus = payment.order.status
      if (newPaymentStatus === 'SETTLEMENT') {
        newOrderStatus = 'PAYMENT_VERIFIED'
      } else if (['DENY', 'CANCEL', 'EXPIRE', 'FAILED'].includes(newPaymentStatus)) {
        newOrderStatus = 'CANCELLED'
      }

      await prisma.$transaction(async (tx) => {
        // Update payment
        await tx.payment.update({
          where: { id: payment.id },
          data: {
            status: newPaymentStatus as any,
            midtransTransactionId: statusResponse.transaction_id,
            midtransPaymentType: statusResponse.payment_type,
            verifiedAt: newPaymentStatus === 'SETTLEMENT' ? new Date() : null,
            updatedAt: new Date(),
          },
        })

        // Update order if needed
        if (newOrderStatus !== payment.order.status) {
          await tx.order.update({
            where: { id: payment.order.id },
            data: {
              status: newOrderStatus as any,
              updatedAt: new Date(),
            },
          })
        }
      })
    }

    return NextResponse.json({
      success: true,
      data: {
        paymentStatus: newPaymentStatus,
        orderStatus: payment.order.status,
        transactionId: statusResponse.transaction_id,
        paymentType: statusResponse.payment_type,
        transactionTime: statusResponse.transaction_time,
        grossAmount: statusResponse.gross_amount,
        statusMessage: statusResponse.status_message,
      },
    })

  } catch (error) {
    console.error('Check Midtrans status error:', error)

    // Handle Midtrans specific errors
    try {
      handleMidtransError(error)
    } catch (midtransError) {
      if (midtransError instanceof Error) {
        return NextResponse.json(
          { error: `Midtrans Error: ${midtransError.message}` },
          { status: 400 }
        )
      }
    }

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        { error: 'Invalid data', details: error.errors },
        { status: 400 }
      )
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}
