# Integrasi Midtrans - Acikoo E-Commerce

## Overview
Dokumen ini menjelaskan integrasi Midtrans Snap Embed ke dalam sistem e-commerce Acikoo sebagai metode pembayaran ketiga, selain QRIS manual dan COD yang sudah ada.

## Perubahan Database Schema

### 1. Enum PaymentMethod
```sql
-- Menambahkan MIDTRANS ke enum PaymentMethod
enum PaymentMethod {
  QRIS
  COD
  MIDTRANS  -- ✅ BARU
}
```

### 2. Enum PaymentStatus
```sql
-- Menambahkan status Midtrans ke enum PaymentStatus
enum PaymentStatus {
  PENDING
  VERIFIED
  FAILED
  SETTLEMENT  -- ✅ BARU (Pembayaran berhasil)
  CAPTURE     -- ✅ BARU (Kartu kredit berhasil)
  DENY        -- ✅ BARU (Pembayaran ditolak)
  CANCEL      -- ✅ BARU (Pembayaran dibatalkan)
  EXPIRE      -- ✅ BARU (Pembayaran kedaluwarsa)
}
```

### 3. Model Payment - Kolom Baru
```sql
model Payment {
  -- <PERSON>lom existing
  id           String        @id @default(cuid())
  orderId      String        @unique
  method       PaymentMethod
  status       PaymentStatus @default(PENDING)
  amount       Decimal       @db.Decimal(10, 2)
  paymentProof String?       // For QRIS payment proof upload
  verifiedAt   DateTime?
  verifiedBy   String?       // Admin who verified
  
  -- ✅ KOLOM BARU UNTUK MIDTRANS
  midtransOrderId       String?   // Midtrans order_id
  midtransSnapToken     String?   // Snap token for frontend
  midtransSnapUrl       String?   // Snap redirect URL
  midtransTransactionId String?   // Midtrans transaction_id
  midtransPaymentType   String?   // Payment type (credit_card, bank_transfer, etc)
  midtransSignatureKey  String?   // Signature key for validation
  midtransRawNotification String? @db.Text // Raw notification from Midtrans
  
  createdAt    DateTime      @default(now())
  updatedAt    DateTime      @updatedAt

  // Relations
  order Order @relation(fields: [orderId], references: [id], onDelete: Cascade)

  @@map("payments")
}
```

## API Endpoints Baru

### 1. POST /api/payments/midtrans/create
**Fungsi**: Membuat transaksi Midtrans Snap
**Input**:
```json
{
  "orderId": "clx1234567890"
}
```
**Output**:
```json
{
  "success": true,
  "data": {
    "snapToken": "66e4fa55-fdac-4ef9-91b5-733b97d1b862",
    "snapUrl": "https://app.sandbox.midtrans.com/snap/v2/vtweb/66e4fa55-fdac-4ef9-91b5-733b97d1b862",
    "orderId": "ACK-123456-*************",
    "clientKey": "Mid-client-hcHelPrFpmScUJLn"
  }
}
```

### 2. POST /api/payments/midtrans/notification
**Fungsi**: Menerima webhook notification dari Midtrans
**Input**: Midtrans notification payload
**Proses**:
1. Validasi signature key
2. Update payment status
3. Update order status jika diperlukan
4. Return success response ke Midtrans

### 3. POST /api/payments/midtrans/status
**Fungsi**: Cek status pembayaran dari Midtrans API
**Input**:
```json
{
  "orderId": "clx1234567890"
}
```
**Output**:
```json
{
  "success": true,
  "data": {
    "paymentStatus": "SETTLEMENT",
    "orderStatus": "PAYMENT_VERIFIED",
    "transactionId": "b91c16dd-8b8c-4d8e-9c4c-8b8c4d8e9c4c",
    "paymentType": "credit_card",
    "transactionTime": "2024-01-15 10:30:00",
    "grossAmount": "45000.00",
    "statusMessage": "Success, transaction is found"
  }
}
```

## Perubahan Frontend

### 1. Halaman Checkout (/checkout)
**File**: `src/app/checkout/page.tsx`

**Perubahan**:
- ✅ Menambahkan radio button "Midtrans" di payment method
- ✅ Menambahkan informasi metode pembayaran Midtrans
- ✅ Update logic `handleConfirmOrder()` untuk Midtrans
- ✅ Redirect ke Midtrans Snap URL setelah order dibuat

### 2. Halaman Payment Callback
**File Baru**:
- `src/app/payment/finish/page.tsx` - Success callback
- `src/app/payment/error/page.tsx` - Error callback  
- `src/app/payment/pending/page.tsx` - Pending callback

## Alur Transaksi Midtrans

### Customer Flow
```
1. Customer pilih "Midtrans" di checkout
2. Customer klik "Konfirmasi Pesanan"
3. System buat order dengan paymentMethod: MIDTRANS
4. System panggil API /api/payments/midtrans/create
5. System redirect customer ke Midtrans Snap URL
6. Customer pilih metode pembayaran di Midtrans
7. Customer selesaikan pembayaran
8. Midtrans redirect ke callback URL (/payment/finish, /payment/error, /payment/pending)
```

### System Flow (Webhook)
```
1. Midtrans kirim notification ke /api/payments/midtrans/notification
2. System validasi signature key
3. System update payment status berdasarkan transaction_status
4. System update order status jika pembayaran berhasil (SETTLEMENT)
5. System return success response ke Midtrans
```

### Status Mapping
| Midtrans Status | Payment Status | Order Status | Keterangan |
|----------------|----------------|--------------|------------|
| pending | PENDING | PENDING_PAYMENT | Menunggu pembayaran |
| capture | SETTLEMENT | PAYMENT_VERIFIED | Kartu kredit berhasil |
| settlement | SETTLEMENT | PAYMENT_VERIFIED | Pembayaran berhasil |
| deny | DENY | CANCELLED | Pembayaran ditolak |
| cancel | CANCEL | CANCELLED | Pembayaran dibatalkan |
| expire | EXPIRE | CANCELLED | Pembayaran kedaluwarsa |
| failure | FAILED | CANCELLED | Pembayaran gagal |

## Configuration

### Environment Variables
```bash
# Midtrans Configuration
MIDTRANS_SERVER_KEY="Mid-server-pFhjqT8tE5obYv9gkdIpwwRL"
MIDTRANS_CLIENT_KEY="Mid-client-hcHelPrFpmScUJLn"  
MIDTRANS_MERCHANT_ID="G524994600"

# NextAuth URL (untuk callback)
NEXTAUTH_URL="http://localhost:3000"
```

### Midtrans Dashboard Settings
1. **Notification URL**: `https://yourdomain.com/api/payments/midtrans/notification`
2. **Finish Redirect URL**: `https://yourdomain.com/payment/finish`
3. **Error Redirect URL**: `https://yourdomain.com/payment/error`
4. **Pending Redirect URL**: `https://yourdomain.com/payment/pending`

## Testing dengan Sandbox

### 1. Setup Ngrok untuk Webhook
```bash
# Install ngrok
npm install -g ngrok

# Expose local server
ngrok http 3000

# Copy HTTPS URL untuk webhook
# Contoh: https://abc123.ngrok.io
```

### 2. Update Midtrans Dashboard
- Notification URL: `https://abc123.ngrok.io/api/payments/midtrans/notification`
- Finish URL: `https://abc123.ngrok.io/payment/finish`
- Error URL: `https://abc123.ngrok.io/payment/error`
- Pending URL: `https://abc123.ngrok.io/payment/pending`

### 3. Test Cards (Sandbox)
```
# Visa Success
4811 1111 1111 1114
CVV: 123, Exp: 01/25

# Mastercard Success  
5211 1111 1111 1117
CVV: 123, Exp: 01/25

# Visa Failure
4911 1111 1111 1113
CVV: 123, Exp: 01/25
```

### 4. Test E-Wallets (Sandbox)
- **GoPay**: Akan redirect ke simulator
- **OVO**: Akan redirect ke simulator
- **DANA**: Akan redirect ke simulator

### 5. Test Bank Transfer (Sandbox)
- **BCA**: Akan generate VA number
- **Mandiri**: Akan generate bill code
- **BNI**: Akan generate VA number

## Migration Script

```bash
# Generate Prisma client dengan schema baru
npm run db:generate

# Push schema changes ke database
npm run db:push

# Atau buat migration file
npx prisma migrate dev --name add_midtrans_fields
```

## Monitoring dan Logging

### 1. Webhook Logs
```javascript
// Log semua notification dari Midtrans
console.log('Midtrans notification received:', notification)

// Log signature validation
console.log('Signature validation:', isValidSignature)

// Log status updates
console.log(`Payment updated: ${paymentId}, Status: ${newStatus}`)
```

### 2. Error Handling
- Validasi signature key gagal → Return 400
- Payment tidak ditemukan → Return 404  
- Database error → Return 500
- Semua error di-log untuk debugging

### 3. Health Check
```bash
# Test webhook endpoint
curl https://yourdomain.com/api/payments/midtrans/notification

# Response:
{
  "success": true,
  "message": "Midtrans notification endpoint is active",
  "timestamp": "2024-01-15T10:30:00.000Z"
}
```

## Keamanan

### 1. Signature Validation
- Semua notification dari Midtrans divalidasi signature key
- Formula: SHA512(order_id + status_code + gross_amount + server_key)
- Notification dengan signature invalid akan ditolak

### 2. HTTPS Required
- Webhook notification hanya diterima via HTTPS
- Callback URLs menggunakan HTTPS di production

### 3. Environment Variables
- Server key dan client key disimpan di environment variables
- Tidak di-commit ke repository

## Troubleshooting

### 1. Webhook Tidak Diterima
- Cek ngrok masih running
- Cek URL di Midtrans dashboard benar
- Cek firewall tidak block incoming requests

### 2. Signature Validation Gagal
- Cek server key di environment variables
- Cek format signature validation
- Cek order_id, status_code, gross_amount sesuai

### 3. Payment Status Tidak Update
- Cek webhook notification diterima
- Cek database connection
- Cek error logs di console

### 4. Redirect Tidak Bekerja
- Cek callback URLs di Midtrans dashboard
- Cek routing di Next.js
- Cek session authentication

## Kompatibilitas

### Sistem Existing
- ✅ QRIS manual tetap berfungsi normal
- ✅ COD tetap berfungsi normal  
- ✅ Admin dashboard mendukung semua payment methods
- ✅ Order tracking mendukung semua payment methods
- ✅ Database migration backward compatible

### Browser Support
- ✅ Chrome, Firefox, Safari, Edge
- ✅ Mobile browsers (iOS Safari, Chrome Mobile)
- ✅ Responsive design untuk semua device

Integrasi Midtrans ini menambahkan fleksibilitas pembayaran tanpa mengganggu sistem existing, memberikan customer lebih banyak pilihan pembayaran yang aman dan terpercaya.
