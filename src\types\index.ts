// Database types from Prisma
import type {
  User,
  Account,
  Session,
  Category,
  Product,
  Message,
  Conversation,
  ContentSection,
  Order,
  OrderItem,
  Payment,
  UserRole,
  MessageStatus,
  OrderStatus,
  PaymentMethod,
  PaymentStatus,
} from '@prisma/client'

// Re-export for convenience
export type {
  User,
  Account,
  Session,
  Category,
  Product,
  Message,
  Conversation,
  ContentSection,
  Order,
  OrderItem,
  Payment,
  UserRole,
  MessageStatus,
  OrderStatus,
  PaymentMethod,
  PaymentStatus,
}

// Extended types for API responses
export interface ProductWithCategory extends Product {
  category: Category
}

export interface MessageWithReplies extends Message {
  user: User
  replies: Message[]
  parent?: Message
}

export interface OrderWithDetails extends Order {
  orderItems: (OrderItem & {
    product: Product
  })[]
  payment?: Payment
  user: User
}

// Cart types
export interface CartItem {
  id: string
  productId: string
  name: string
  price: number
  quantity: number
  image?: string
  stock: number
}

export interface Cart {
  items: CartItem[]
  total: number
  itemCount: number
}

// Checkout types
export interface CheckoutData {
  deliveryAddress: string
  deliveryLat?: number
  deliveryLng?: number
  notes?: string
  paymentMethod: PaymentMethod
}

// Midtrans specific types
export interface MidtransSnapResponse {
  token: string
  redirect_url: string
}

export interface MidtransPaymentData {
  snapToken: string
  snapUrl: string
  orderId: string
  clientKey: string
}

// Location types
export interface Location {
  lat: number
  lng: number
  address?: string
}

export interface DeliveryInfo {
  address: string
  location: Location
  distance: number
  cost: number
  isWithinRadius: boolean
}

// API Response types
export interface ApiResponse<T = any> {
  success: boolean
  data?: T
  error?: string
  message?: string
}

export interface PaginatedResponse<T> extends ApiResponse<T[]> {
  pagination: {
    page: number
    limit: number
    total: number
    totalPages: number
  }
}

// Form types
export interface LoginForm {
  email: string
  password: string
}

export interface RegisterForm {
  name: string
  email: string
  password: string
  confirmPassword: string
  phone?: string
}



export interface ProductForm {
  name: string
  description?: string
  price: number
  categoryId: string
  stock: number
  weight?: number
  images?: string[]
}

export interface CategoryForm {
  name: string
  description?: string
  image?: string
}

// Dashboard types
export interface DashboardStats {
  totalRevenue: number
  totalCustomers: number
  revenueGrowth: number
  customerGrowth: number
}

export interface ChartData {
  name: string
  value: number
  date?: string
}

// Socket.IO event types
export interface SocketEvents {
  newMessage: (data: Message) => void
}

// Filter and search types
export interface ProductFilters {
  categoryId?: string
  minPrice?: number
  maxPrice?: number
  search?: string
  sortBy?: 'name' | 'price' | 'createdAt'
  sortOrder?: 'asc' | 'desc'
  page?: number
  limit?: number
}



// Store configuration
export interface StoreConfig {
  name: string
  address: string
  phone: string
  email: string
  hours: {
    [key: string]: {
      open: string
      close: string
      isOpen: boolean
    }
  }
  deliveryRadius: number
  minDeliveryCost: number
  maxDeliveryCost: number
  location: Location
}



// File upload types
export interface UploadedFile {
  url: string
  filename: string
  size: number
  type: string
}

// Notification types
export interface Notification {
  id: string
  title: string
  message: string
  type: 'info' | 'success' | 'warning' | 'error'
  timestamp: Date
  read: boolean
}

// Analytics types
export interface SalesAnalytics {
  period: 'day' | 'week' | 'month' | 'year'
  revenue: ChartData[]
  topProducts: Array<{
    product: Product
    quantity: number
    revenue: number
  }>
  topCategories: Array<{
    category: Category
    quantity: number
    revenue: number
  }>
}

// Error types
export interface AppError {
  code: string
  message: string
  details?: any
}

// Component prop types
export interface BaseComponentProps {
  className?: string
  children?: React.ReactNode
}

export interface LoadingState {
  isLoading: boolean
  error?: string | null
}

// Navigation types
export interface NavItem {
  label: string
  href: string
  icon?: React.ComponentType
  badge?: number
  children?: NavItem[]
}

// Theme types
export interface ThemeConfig {
  colors: {
    primary: string
    secondary: string
    accent: string
    background: string
    foreground: string
  }
  fonts: {
    heading: string
    body: string
  }
}
