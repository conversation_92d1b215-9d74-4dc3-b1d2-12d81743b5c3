'use client'

import { useEffect } from 'react'
import { useRouter, useSearchParams } from 'next/navigation'
import { useSession } from 'next-auth/react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { XCircle, ArrowLeft, ShoppingBag, RefreshCw } from 'lucide-react'

export default function PaymentErrorPage() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const { data: session } = useSession()

  useEffect(() => {
    if (!session) {
      router.push('/auth/signin')
      return
    }
  }, [session, router])

  const orderId = searchParams.get('order_id')
  const statusCode = searchParams.get('status_code')
  const transactionStatus = searchParams.get('transaction_status')

  const handleRetryPayment = () => {
    if (orderId) {
      // Extract order number from Midtrans order ID
      const orderNumber = orderId.split('-').slice(0, -1).join('-')
      router.push(`/orders?retry=${orderNumber}`)
    } else {
      router.push('/orders')
    }
  }

  if (!session) {
    return null
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="container mx-auto px-4 max-w-2xl">
        {/* Header */}
        <div className="flex items-center mb-6">
          <Button
            variant="ghost"
            onClick={() => router.push('/orders')}
            className="mr-4"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Kembali ke Pesanan
          </Button>
        </div>

        {/* Error Card */}
        <Card>
          <CardHeader className="text-center">
            <div className="w-16 h-16 rounded-full bg-red-50 flex items-center justify-center mx-auto mb-4">
              <XCircle className="w-8 h-8 text-red-600" />
            </div>
            <CardTitle className="text-2xl text-red-600">Pembayaran Gagal</CardTitle>
            <p className="text-gray-600">
              Terjadi kesalahan saat memproses pembayaran Anda. Silakan coba lagi.
            </p>
          </CardHeader>

          <CardContent className="space-y-4">
            {/* Error Details */}
            {(orderId || statusCode || transactionStatus) && (
              <div className="bg-red-50 border border-red-200 p-4 rounded-lg space-y-2">
                <h4 className="font-medium text-sm text-red-800">Detail Error</h4>
                <div className="text-sm space-y-1">
                  {orderId && (
                    <div className="flex justify-between">
                      <span className="text-red-600">Order ID:</span>
                      <span className="font-mono text-red-800">{orderId}</span>
                    </div>
                  )}
                  {statusCode && (
                    <div className="flex justify-between">
                      <span className="text-red-600">Status Code:</span>
                      <span className="text-red-800">{statusCode}</span>
                    </div>
                  )}
                  {transactionStatus && (
                    <div className="flex justify-between">
                      <span className="text-red-600">Transaction Status:</span>
                      <span className="text-red-800 capitalize">{transactionStatus.replace('_', ' ')}</span>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Possible Causes */}
            <div className="bg-gray-50 p-4 rounded-lg">
              <h4 className="font-medium text-sm mb-2">Kemungkinan Penyebab:</h4>
              <ul className="text-sm text-gray-600 space-y-1">
                <li>• Saldo atau limit kartu tidak mencukupi</li>
                <li>• Koneksi internet terputus saat transaksi</li>
                <li>• Informasi kartu atau rekening tidak valid</li>
                <li>• Transaksi ditolak oleh bank penerbit</li>
                <li>• Waktu pembayaran telah habis</li>
              </ul>
            </div>

            {/* Action Buttons */}
            <div className="flex space-x-3 pt-4">
              <Button
                onClick={handleRetryPayment}
                className="flex-1 bg-red-600 hover:bg-red-700"
              >
                <RefreshCw className="w-4 h-4 mr-2" />
                Coba Lagi
              </Button>
              <Button
                variant="outline"
                onClick={() => router.push('/')}
                className="flex-1"
              >
                <ShoppingBag className="w-4 h-4 mr-2" />
                Belanja Lagi
              </Button>
            </div>

            {/* Help Text */}
            <div className="text-center pt-4 border-t">
              <p className="text-sm text-gray-600">
                Jika masalah terus berlanjut, silakan hubungi customer service kami
              </p>
              <Button
                variant="link"
                onClick={() => router.push('/messages')}
                className="text-red-600 hover:text-red-700"
              >
                Hubungi Customer Service
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
