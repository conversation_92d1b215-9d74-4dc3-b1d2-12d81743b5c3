'use client'

import { useEffect, useState } from 'react'
import { useR<PERSON>er, useSearchParams } from 'next/navigation'
import { useSession } from 'next-auth/react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Clock, ArrowLeft, ShoppingBag, RefreshCw } from 'lucide-react'

export default function PaymentPendingPage() {
  const router = useRouter()
  const searchParams = useSearchParams()
  const { data: session } = useSession()
  const [checking, setChecking] = useState(false)

  useEffect(() => {
    if (!session) {
      router.push('/auth/signin')
      return
    }
  }, [session, router])

  const orderId = searchParams.get('order_id')
  const statusCode = searchParams.get('status_code')
  const transactionStatus = searchParams.get('transaction_status')

  const handleCheckStatus = async () => {
    if (!orderId) return

    setChecking(true)
    try {
      // Extract order number from Midtrans order ID
      const orderNumber = orderId.split('-').slice(0, -1).join('-')
      
      // Find order by order number
      const ordersResponse = await fetch('/api/orders')
      const ordersResult = await ordersResponse.json()
      
      if (ordersResult.success) {
        const order = ordersResult.data.find((o: any) => o.orderNumber === orderNumber)
        
        if (order) {
          // Check Midtrans payment status
          const statusResponse = await fetch('/api/payments/midtrans/status', {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ orderId: order.id })
          })

          const statusResult = await statusResponse.json()

          if (statusResult.success) {
            const paymentStatus = statusResult.data.paymentStatus
            
            if (paymentStatus === 'SETTLEMENT') {
              router.push(`/payment/finish?order_id=${orderId}&status_code=200&transaction_status=settlement`)
            } else if (['DENY', 'CANCEL', 'EXPIRE', 'FAILED'].includes(paymentStatus)) {
              router.push(`/payment/error?order_id=${orderId}&status_code=400&transaction_status=${paymentStatus.toLowerCase()}`)
            } else {
              // Still pending, show message
              alert('Pembayaran masih dalam proses. Silakan coba lagi dalam beberapa menit.')
            }
          }
        }
      }
    } catch (error) {
      console.error('Check status error:', error)
      alert('Gagal mengecek status pembayaran. Silakan coba lagi.')
    } finally {
      setChecking(false)
    }
  }

  if (!session) {
    return null
  }

  return (
    <div className="min-h-screen bg-gray-50 py-8">
      <div className="container mx-auto px-4 max-w-2xl">
        {/* Header */}
        <div className="flex items-center mb-6">
          <Button
            variant="ghost"
            onClick={() => router.push('/orders')}
            className="mr-4"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Kembali ke Pesanan
          </Button>
        </div>

        {/* Pending Card */}
        <Card>
          <CardHeader className="text-center">
            <div className="w-16 h-16 rounded-full bg-yellow-50 flex items-center justify-center mx-auto mb-4">
              <Clock className="w-8 h-8 text-yellow-600" />
            </div>
            <CardTitle className="text-2xl text-yellow-600">Pembayaran Pending</CardTitle>
            <p className="text-gray-600">
              Pembayaran Anda sedang diproses. Mohon tunggu konfirmasi dari sistem pembayaran.
            </p>
          </CardHeader>

          <CardContent className="space-y-4">
            {/* Transaction Details */}
            {(orderId || statusCode || transactionStatus) && (
              <div className="bg-yellow-50 border border-yellow-200 p-4 rounded-lg space-y-2">
                <h4 className="font-medium text-sm text-yellow-800">Detail Transaksi</h4>
                <div className="text-sm space-y-1">
                  {orderId && (
                    <div className="flex justify-between">
                      <span className="text-yellow-600">Order ID:</span>
                      <span className="font-mono text-yellow-800">{orderId}</span>
                    </div>
                  )}
                  {statusCode && (
                    <div className="flex justify-between">
                      <span className="text-yellow-600">Status Code:</span>
                      <span className="text-yellow-800">{statusCode}</span>
                    </div>
                  )}
                  {transactionStatus && (
                    <div className="flex justify-between">
                      <span className="text-yellow-600">Transaction Status:</span>
                      <span className="text-yellow-800 capitalize">{transactionStatus.replace('_', ' ')}</span>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Information */}
            <div className="bg-blue-50 border border-blue-200 p-4 rounded-lg">
              <h4 className="font-medium text-sm text-blue-800 mb-2">Informasi Penting:</h4>
              <ul className="text-sm text-blue-700 space-y-1">
                <li>• Proses pembayaran dapat memakan waktu 1-5 menit</li>
                <li>• Jangan menutup browser atau melakukan pembayaran ulang</li>
                <li>• Status akan otomatis terupdate setelah pembayaran berhasil</li>
                <li>• Anda akan menerima notifikasi email setelah pembayaran dikonfirmasi</li>
              </ul>
            </div>

            {/* Action Buttons */}
            <div className="flex space-x-3 pt-4">
              <Button
                onClick={handleCheckStatus}
                disabled={checking || !orderId}
                className="flex-1 bg-yellow-600 hover:bg-yellow-700"
              >
                {checking ? (
                  <>
                    <div className="w-4 h-4 border-2 border-white border-t-transparent rounded-full animate-spin mr-2"></div>
                    Mengecek...
                  </>
                ) : (
                  <>
                    <RefreshCw className="w-4 h-4 mr-2" />
                    Cek Status
                  </>
                )}
              </Button>
              <Button
                variant="outline"
                onClick={() => router.push('/orders')}
                className="flex-1"
              >
                Lihat Pesanan
              </Button>
            </div>

            {/* Help Text */}
            <div className="text-center pt-4 border-t">
              <p className="text-sm text-gray-600 mb-2">
                Pembayaran tidak kunjung berhasil?
              </p>
              <div className="flex space-x-2 justify-center">
                <Button
                  variant="link"
                  onClick={() => router.push('/messages')}
                  className="text-red-600 hover:text-red-700 text-sm"
                >
                  Hubungi Customer Service
                </Button>
                <span className="text-gray-400">|</span>
                <Button
                  variant="link"
                  onClick={() => router.push('/')}
                  className="text-red-600 hover:text-red-700 text-sm"
                >
                  <ShoppingBag className="w-3 h-3 mr-1" />
                  Belanja Lagi
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
